ion-grid {
  width: 100%;
}

.scale {
  background-color: rgba(26, 26, 26, 0.5);
  color: #FFF !important;
  color: #aaaaaa;
  height: 67px;
  line-height: 70px;
  margin: 5px;
  cursor: pointer;
  border-radius: 6px;
  font-family: '<PERSON><PERSON>';
  font-weight: 900;
  font-size: 16px;
  text-align: center;
  border: 1.5px solid #111;
}

.green {
  background-color: rgba(255, 255,255, 0.1);
  color: white !important;
  border: 1.5px solid rgba(255, 255, 255, 0.8);
  height: 67px;
  line-height: 70px;
  margin: 5px;
  cursor: pointer;
  font-family: '<PERSON><PERSON>';
  font-weight: 900;
  font-size: 16px;
  border-radius: 6px;
  text-align: center;
}
.bottom-container {
  font-size: 14px;
  display: flex;
  flex-direction: row;
  align-content: center;
  justify-content: space-between;
  margin: 8px;
}

.bottom-label-container {
  font-size: 14px;
  color: #aaaaaa;
  display: flex;
  flex-direction: row;
  align-content: center;
  justify-content: center;
  margin: 5px 5px 5px 5px;
  margin-bottom: 15px;
}

.center-content {
  flex: auto;
  justify-content: center;
  align-items: center;
}

.container {
  display: flex;
  justify-content: center;
}

.half-width {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  align-content: center;
  margin-left: 2.25em;
  margin-right: 0.5em;
  flex-grow: 1;
}

.space-between-row {
  flex-direction: row;
  justify-content: space-between;
}

.max-size-col {
  max-height: 80px;
}

.scale-font {
  font-size: 25px !important;
}

.mat-icon {
  height: 30px;
  width: 30px;
  font-size: 30px;
}

.triangle-top-right {
  position: relative;
}
.triangle-top-right::before {
  content: ' ';
  position: absolute;
  top: 0;
  right: 0;
  border-style: solid;
  border-width: 0 40px 40px 0;
  border-color: transparent rgba(26, 26, 26, 0.5) transparent transparent;
  border-top-right-radius: 5px;
}

.corner-checkbox {
  position: absolute;
  top: 4px;
  right: -3px;
  height: 16px;
  font-size: 16px;
  color: white;
}
