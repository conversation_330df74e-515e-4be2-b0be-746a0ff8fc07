:host {
  .parent-container {
    height: 100%;
    .back-col {
      display: flex;
      justify-content: space-between;

      ion-button.button::part(native) {
        line-height: 0px;
      }
    }

    @media (min-width: 1500px) {
      .image-container {
        height: 11vw;
      }
      
      .component {
        width: 15vw;
        height: 11.3vw;
      }
    }
    
    @media (min-width: 992px) and (max-width:  1500px) {
      .image-container {
        height: 15vw;
      }
    
      .component {
        width: 20vw;
        height: 15.3vw;
      }
    }

    @media (min-width: 769px) and (max-width: 992px) {
      .image-container {
        height: 180px;
      }
    
      .component {
        width: 250px;
        height: 181px;
      }
    }

    @media (min-width: 480px) and (max-width: 768px) {
    .image-container {
        height: 180px;
      }
    
      .component {
        width: 250px;
        height: 183px;
      }
    }

    @media (max-width: 479px) {
      .image-container {
        height: 180px;
      }
    
      .component {
        width: 252px;
        height: 181px;
      }
    }
    
    .image-container {
      background-size: contain;
      width: 100%;
      object-fit: contain;
      background-position: left;
      background-repeat: no-repeat;
      border-radius: 5px;
    }

    .inner-search-container {
      color: white;
      position: relative;
      height: 100%;

      .top-heading-container {
        .heading {
          font-size: 16px;
        }

        .grey-text {
          color: gray;
          font-size: 15px;
        }
        
        .search-container {
          margin-top: 10px;
          position: relative;
          width: 100%;
        }

        ion-input {
          margin-bottom: 5px;
          position: relative;
          --padding-start: 40px;
          --padding-end: 10px;
          
          .search-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #aaaaaa;
            font-size: 18px;
          }
        }
      }

      ion-input {
        background-color: #292929;
        margin-top: 5px;
        border-radius: 4px;
        --color: white;
        --placeholder-color: #aaaaaa;
        --placeholder-opacity: 0.8;
        height: 40px;
        --padding-top: 0;
        --padding-bottom: 0;
      }

      .dynamic-list-container {
        background-color: #292929;
        padding: 10px;
        border-radius: 5px;
        width: 100%;
        height: calc(100% - 135px);
        overflow-y: auto;

        .image-heading-container {
          margin-bottom: 5px;

          .image-heading {
            font-weight: bold;
            font-size: 14px;
            color: #aaaaaa;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis !important;
            padding: 0px 10px;
          }
        }

        .component-container {
          margin-bottom: 20px;
          width: 100%;
          display: flex;
          flex-direction: column;

          .component:hover {
            border: 1px solid #f99e00;
          }

          .component {
            border-radius: 5px;
            display: flex;
            flex-direction: row;
            align-items: center;
            align-content: center;
            cursor: pointer;

            .component-left {
              display: flex;
              flex-direction: row;
              align-items: center;
              align-content: center;

              .inner-container {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                .heading {
                  font-weight: bold;
                  font-size: 14px;
                  color: white;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis !important;
                  padding: 0px 10px;
                }
              }
            }
          }

          .selected {
            border: 1px solid #f99e00;
          }
        }
      }
    }
  }
}
