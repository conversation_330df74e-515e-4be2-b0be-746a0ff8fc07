import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { IQuestion } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { Observable, map } from 'rxjs';

@Component({
    selector: 'app-matching-list-settings',
    templateUrl: './matching-list-settings.component.html',
    styleUrls: ['./matching-list-settings.component.scss'],
    standalone: false
})
export class MatchingListSettingsComponent implements OnInit {
  @Input() question: IQuestion;
  @Input() backgroundColor = '#181818';
  @Output() questionChanged = new EventEmitter<any>();
  formGroup: UntypedFormGroup;
  options$: Observable<KeyValue[]>;
  purposes: KeyValue[];

  constructor(
    private dataService: DataService,
    private formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit() {
    this.initData();
    this.setupForm();
  }

  initData() {
    this.options$ = this.dataService.getTagChildren(null).pipe(
      map(tags => {
        return tags.map(t => {
          return { id: t.id, value: t.name } as KeyValue;
        });
      })
    );
    this.purposes = [{ id: 'Interest', value: 'Interest' } as KeyValue, { id: 'Knowledge', value: 'Knowledge' } as KeyValue, { id: 'Identity', value: 'Identity' } as KeyValue];
  }

  setupForm() {
    this.formGroup = this.formBuilder.group({
      isDynamicOptions: [this.question.isDynamicOptions],
      isAuthorEditable: [this.question.isAuthorEditable],
      isRandom: [this.question.isRandom],
      isOtherOption: [this.question.isOtherOption],
      isLimitTo: [this.question.isLimitTo],
      isOptionPhoto: [this.question.isOptionPhoto],
      limit: [this.question.limit],
      purpose: [this.question.purpose],
      dynamicOptionId: [this.question?.dynamicOptionsParentId],
    });

    this.subscribeToForm();
  }

  subscribeToForm() {
    this.formGroup.valueChanges.subscribe(() => {
      this.question.isDynamicOptions = this.formGroup.controls.isDynamicOptions.value;
      this.question.isAuthorEditable = this.formGroup.controls.isAuthorEditable.value;
      this.question.isRandom = this.formGroup.controls.isRandom.value;
      this.question.isOtherOption = this.formGroup.controls.isOtherOption.value;
      this.question.isLimitTo = this.formGroup.controls.isLimitTo.value;
      this.question.isOptionPhoto = this.formGroup.controls.isOptionPhoto.value;
      this.question.limit = this.formGroup.controls.limit.value;
      this.question.purpose = this.formGroup.controls.purpose.value;
      this.question.dynamicOptionsParentId = this.formGroup.controls.dynamicOptionId.value;

      this.questionChanged.emit(null);
    });
  }
}
