import { Component, Input, OnInit } from '@angular/core';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { environment } from '@env/environment';
import { ModalController } from '@ionic/angular';
import { ThumbnailStyleBaseComponent } from '../../base/thumbnail-style-base.component';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-row-photo-container',
    templateUrl: './photo-container.component.html',
    styleUrls: ['./photo-container.component.scss'],
    standalone: false
})
export class PhotoContainerComponent extends ThumbnailStyleBaseComponent implements OnInit {
  @Input() instanceName: string;
  @Input() iconAssetId: string;
  @Input() externalLogoUrl?: string;
  @Input() action: string;
  @Input() actionUrl: string;
  @Input() sameUrlNavigation: boolean;
  @Input() isEducator: boolean = false;
  @Input() hasAdminAccess: boolean = false;
  @Input() hideAddButton: boolean = false;
  @Input() isAssignmentRow?: boolean = false;

  constructor(dataService: DataService, authService: AuthService, instanceService: InstanceService, modalController: ModalController, layoutService: LayoutService) {
    super(dataService, authService, instanceService, modalController, layoutService);
  }

  ngOnInit() {
    if (this.externalLogoUrl) {
      this.imageUrl = this.externalLogoUrl;
    } else if (this.iconAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.iconAssetId}/content?height=600`;
    } else {
      this.imageUrl = 'assets/images/no-image.png';
    }
  }
}
