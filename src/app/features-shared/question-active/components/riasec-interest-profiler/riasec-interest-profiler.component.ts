import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IQuestion, IUserAnswer } from '@app/core/contracts/contract';

export interface RiasecScale {
  icon: string;
  value: number;
}

@Component({
    selector: 'app-riasec-interest-profiler',
    templateUrl: './riasec-interest-profiler.component.html',
    styleUrls: ['./riasec-interest-profiler.component.scss'],
    standalone: false
})
export class RiasecInterestProfilerComponent {
  @Input() backgroundColor = '#181818';
  @Input() question: IQuestion;
  @Input() userAnswer: IUserAnswer;
  @Output() userAnswerChanged = new EventEmitter<IUserAnswer>();
  scaleArray: RiasecScale[] = [
    { icon: 'sentiment_very_dissatisfied', value: 0 } as RiasecScale,
    { icon: 'sentiment_dissatisfied', value: 1 } as RiasecScale,
    { icon: 'sentiment_neutral', value: 2 } as RiasecScale,
    { icon: 'sentiment_satisfied', value: 3 } as RiasecScale,
    { icon: 'sentiment_very_satisfied', value: 4 } as RiasecScale,
  ];
  questionDirty = false;

  scaleClicked(scale: RiasecScale) {
    if (!this.userAnswer) {
      this.userAnswer = {
        id: '',
        instanceId: '',
        assessmentQuestionId: null,
        userId: '',
        answerText: scale.value.toString(),
        userQuestionAnswers: [],
        questionId: this.question.id,
        instanceComponentId: null,
        marks: null,
        gradePercentage: null,
        isSubmitted: null,
        isAutoGraded: false,
        isEducatorGraded: false,
        answerStatusTypeBw: null,
        feedback: null,
      } as IUserAnswer;
    } else if (this.questionDirty && this.userAnswer.answerText === scale.toString()) {
      this.userAnswer.answerText = '';
    } else {
      this.userAnswer.answerText = scale.value.toString();
    }

    this.questionDirty = true;
    this.userAnswerChanged.emit(this.userAnswer);
  }

  scaleSelected(scale: RiasecScale): boolean {
    return scale.value.toString() === (this.userAnswer?.answerText ?? '');
  }
}
