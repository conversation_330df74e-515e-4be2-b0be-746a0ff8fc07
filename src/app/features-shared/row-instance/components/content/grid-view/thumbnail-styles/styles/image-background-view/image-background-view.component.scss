.content {
  background-color: #111;
  background-image: var(--background-image);
  background-size: cover;
  min-height: 0;
  outline: 1px #333 solid;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'roboto';
  margin: 0;
  color: #aaa;
  width: 100%;
}

.text-container {
  position: absolute;
  bottom: 11px;
}

@media (min-width: 1501px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;

    h1 {
      bottom: 17px;
      color: #fff;
      font-size: 1.25em;
      font-weight: 900;
    }

    h5 {
      color: #aaa;
      font-style: italic;
      font-size: 0.875em;
      font-weight: 900;
    }

    h6 {
      color: #aaa;
      bottom: 0px;
      font-style: italic;
      font-size: 0.875em;
      font-weight: 500;
    }
  }
}

@media (min-width: 1281px) and (max-width: 1500px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;

    h1 {
      bottom: 17px;
      color: #fff;
      font-size: 1.125em;
      font-weight: 900;
    }

    h5 {
      color: #aaa;
      font-style: italic;
      font-size: 0.875em;
      font-weight: 900;
    }

    h6 {
      color: #aaa;
      bottom: 0px;
      text-overflow: ellipsis;
      font-style: italic;
      font-size: 0.875em;
      font-weight: 500;
    }
  }
}

@media (min-width: 993px) and (max-width: 1280px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;

    h1 {
      bottom: 17px;
      color: #fff;
      font-size: 1.063em;
      font-weight: 900;
    }

    h5 {
      color: #aaa;
      font-style: italic;
      font-size: 0.875em;
      font-weight: 900;
    }

    h6 {
      color: #aaa;
      bottom: 0px;
      text-overflow: ellipsis;
      font-style: italic;
      font-size: 0.875em;
      font-weight: 500;
    }
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;

    h1 {
      bottom: 17px;
      color: #fff;
      font-size: 1.063em;
      font-weight: 900;
    }

    h5 {
      color: #aaa;
      font-style: italic;
      font-size: 0.875em;
      font-weight: 900;
    }

    h6 {
      color: #aaa;
      bottom: 0px;
      text-overflow: ellipsis;
      font-style: italic;
      font-size: 0.875em;
      font-weight: 500;
    }
  }
}

@media (min-width: 480px) and (max-width: 768px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;
    padding-bottom: 7px;

    h1 {
      bottom: 16px;
      color: #fff;
      font-size: 1em;
      font-weight: 900;
    }

    h5 {
      color: #aaa;
      font-style: italic;
      font-size: 0.75em;
      font-weight: 900;
      padding-bottom: 1px;
    }

    h6 {
      color: #aaa;
      bottom: 0px;
      text-overflow: ellipsis;
      font-style: italic;
      font-size: 0.75em;
      font-weight: 500;
    }
  }
}

@media (max-width: 479px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;
    padding-bottom: 7px;

    h1 {
      bottom: 16px;
      color: #fff;
      font-size: 1em;
      font-weight: 900;
    }

    h5 {
      color: #aaa;
      font-style: italic;
      font-size: 0.75em;
      font-weight: 900;
      padding-bottom: 1px;
    }

    h6 {
      color: #aaa;
      bottom: 0px;
      text-overflow: ellipsis;
      font-style: italic;
      font-size: 0.75em;
      font-weight: 500;
    }
  }
}

h2 {
  font-size: 2.5em;
}

h3 {
  font-size: 2em;
}

h4 {
  font-size: 1.8em;
}

.portrait {
  aspect-ratio: 4 / 5;
}

.landscape {
  aspect-ratio: 3 / 2;
}

.img-col {
  min-width: 60px;
  .row-icon {
    height: 40px;
    width: 40px;
    border-radius: 5px;
    margin: 0px 10px;
  }
}

.edit-button-container {
  display: flex;
  justify-content: flex-end;
  position: absolute;
  z-index: 1;
  margin-top: 3px;
  right: 5px;

  ion-button {
    --background: #fff;
    --padding-start: 0.75em;
    --padding-end: 0.75em;
    height: 1.9em;
  }

  .delete {
    cursor: pointer;
    color: #000000;
    z-index: 2;
  }

  .delete:hover {
    color: #ee9907;
  }

  .button-text {
    text-transform: none;
    letter-spacing: 0.03em;
  }

  .round-button {
    --padding-start: 0.75em !important;
    --padding-end: 0.75em !important;
  }

  .cdk-drag-handle {
    color: #000000;
    z-index: 2;
    cursor: move;
  }

  mat-icon {
    height: 20px;
    width: 20px;
  }
}

:host ::ng-deep div.parent-container {
  margin-top: 0px;
  margin-right: 10px;
  border: 0px;
  margin-bottom: 0px !important;
}

:host ::ng-deep .parent-container {
  margin-top: 0px;
  margin-right: 10px;
  border: 0px;
  margin-bottom: 0px;
}

.background-gradient {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4) 70%, rgba(0, 0, 0, 0.7) 80%, rgba(0, 0, 0, 0.9) 90%), var(--background-image) no-repeat;
  background-size: cover;
  position: relative;
}

.status-inprogress {
  border: 2px solid orange;
  border-radius: 5px;
}

.status-complete {
  border: 2px solid green;
  border-radius: 5px;
}

.status-foot-container {
  position: relative;
  left: 25px;
  z-index: 999;
  .inner {
    padding: 2px;
    font-size: 12px;
    border-radius: 5px;
    color: white;
  }
}
