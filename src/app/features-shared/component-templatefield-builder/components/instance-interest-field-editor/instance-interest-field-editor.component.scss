.parent-form-container {
  margin: 10px 16px 10px 16px;

  .full-width {
    width: 100%;
  }

  .middle-line {
    margin-top: 20px;
    hr {
      border-top: 3px solid rgb(40, 40, 40);
    }
  }

  .top-header-col {
    align-items: center;
    display: flex;
    .header {
      font-family: '<PERSON><PERSON>';
      font-weight: bold;
      font-size: 25px;
    }
    .sub-header {
      font-family: '<PERSON><PERSON>';
      font-weight: 500;
      font-size: 18px;
      line-height: 1.1;
      letter-spacing: 0.3px;
    }
  }

  .row-number-col {
    padding-right: 15px;
  }

  .bottom-sliders-col {
    display: flex;
    align-items: center;
    ion-card {
      margin: 0px;
      margin-top: 8px;
    }
  }
}
