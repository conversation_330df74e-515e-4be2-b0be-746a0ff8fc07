import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import { AfterContentChecked, AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MatStepper } from '@angular/material/stepper';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import {
  ICommunicationBlock,
  IComponent,
  IInstance,
  IInstanceSection,
  IInstanceSectionComponent,
  IInstanceSectionComponentIn,
  IInstanceSectionIn,
  IInstanceTemplate,
  IQuestion,
  IQuestionAnswer,
  IQuestionAnswerIn,
  IQuestionIn,
  IRouteParams,
  ISection,
  ISidePanelBuilder,
  ITemplateFieldIn,
} from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { SidePanelBuilderType } from '@app/core/enums/builder-selection-types';
import { SectionTypes } from '@app/core/enums/section-types.enum';
import { StepType } from '@app/core/enums/side-panel-wizard-step-type.enum';
import { SystemPropertyType } from '@app/core/enums/system-property-type.enum';
import { AlertService } from '@app/core/services/alert-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { LayoutService } from '@app/core/services/layout-service';
import { RolesService } from '@app/core/services/roles.service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { UnsavedChangesGuard } from '@app/core/services/unsaved-changes.guard';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, debounceTime, forkJoin, takeUntil } from 'rxjs';

export interface Step {
  instanceSection: IInstanceSection;
  section: ISection;
  sectionIndex: number | null;
  sectionComponentsLength: number | null;
  instanceSectionComponentId: string | null;
  component: IComponent | null;
  componentIndex: number | null;
  typeBw: number;
}

@Component({
    selector: 'app-builder-side-panel',
    templateUrl: './builder-side-panel.component.html',
    styleUrls: ['./builder-side-panel.component.scss'],
    providers: [
        {
            provide: STEPPER_GLOBAL_OPTIONS,
            useValue: { displayDefaultIndicatorType: false },
        },
    ],
    standalone: false
})
export class BuilderSidePanelComponent implements OnInit, OnChanges, AfterContentChecked, AfterViewInit, OnDestroy {
  @Input() editPanelBuilderIn: ISidePanelBuilder;
  @Input() editInstanceSectionsIn: IInstanceSection[] = [];
  @Input() instance: IInstance;
  @Input() template: IInstanceTemplate;
  @Input() id: string;
  @Input() isManager: boolean;
  @Input() onlyContent = false;
  @Input() routeParams: IRouteParams;
  @Output() menuClosed = new EventEmitter<any>();
  @Output() detectChanges = new EventEmitter<any>();
  @Output() editPanelBuilderChanged = new EventEmitter<any>();
  @ViewChild('stepper') stepper: MatStepper;
  @ViewChild('content') elementView: ElementRef;

  builderType = SidePanelBuilderType;
  templateForm: UntypedFormGroup;
  section: ISection;
  componentDestroyed$: Subject<boolean> = new Subject();
  selectedIndex = 0;
  instanceSectionsIn: IInstanceSectionIn[] = [];
  sectionTypes = SectionTypes;
  steps: Step[] = [];
  stepTypes = StepType;
  contentHeight = 0;
  sectionHeight = 0;
  selectedInstanceSectionComponent: IInstanceSectionComponent | undefined;
  autosaveTriggered = false;
  constructor(
    private systemPropertyService: SystemPropertiesService,
    private builderService: BuilderService,
    private dataService: DataService,
    private alertService: AlertService,
    private toast: GlobalToastService,
    private cdRef: ChangeDetectorRef,
    private layoutService: LayoutService,
    private rolesService: RolesService,
    private instanceService: InstanceService,
    private unsavedChangesGuard: UnsavedChangesGuard
  ) {}

  ngAfterViewInit(): void {
    this.sectionHeight = this.elementView?.nativeElement.getBoundingClientRect().top;
    this.contentHeight = this.sectionHeight + 100;
    if (this.layoutService.currentScreenSize === 'xs') {
      this.contentHeight += 85;
      this.sectionHeight += 85;
    }
  }

  ngOnInit() {
    this.autosaveTriggered = false;
    this.subscribeToSectionChanges();
    this.createTemplateFormGroup();
    this.setupSteps();
  }

  ngAfterContentChecked() {
    if (!this.autosaveTriggered) {
      this.cdRef.detectChanges();
    }

    this.sectionHeight = this.elementView?.nativeElement.getBoundingClientRect().top;
    this.contentHeight = this.sectionHeight + 100;
    if (this.layoutService.currentScreenSize === 'xs') {
      this.contentHeight += 85;
      this.sectionHeight += 85;
    }
  }

  getState(step: Step): string {
    if (step.typeBw === StepType.Component) {
      return 'empty';
    } else {
      return `section`;
    }
  }

  getStepEditIcon(index: number) {
    const step = this.steps[index];

    if (step.typeBw === this.stepTypes.Component) {
      return '';
    } else {
      return step.sectionIndex;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['template'] && this.steps) {
      this.setupSteps();
      this.createTemplateFormGroup();
    }

    if (changes['editPanelBuilderIn']) {
      switch (this.editPanelBuilderIn?.builderType) {
        case this.builderType.EditComponentSetup:
          this.selectedInstanceSectionComponent = this.template.instanceSections
            .find(x => x.instanceSectionComponents?.some(y => y.component.id === this.editPanelBuilderIn?.id))
            ?.instanceSectionComponents.find(x => x.component.id === this.editPanelBuilderIn?.id);
          break;
        case this.builderType.EditSection:
          this.section = this.template.instanceSections.find(x => x.section.id === this.editPanelBuilderIn?.id)?.section as ISection;
          this.setStep(this.editPanelBuilderIn?.id, true);
          break;
        case this.builderType.AddComponent: {
          const section = this.template.instanceSections.find(x => x.id === this.editPanelBuilderIn.id)?.section;
          if (section?.typeBw === SectionTypes.Dynamic) {
            this.section = section;
            break;
          }
          break;
        }
        case this.builderType.AddSection:
          break;
        default:
          if (this.steps) {
            this.setStep(this.editPanelBuilderIn?.id, true);
          } else {
            this.setupSteps();
          }
          break;
      }
    }
  }

  setupSteps(scrollTo = false) {
    const stepList: Step[] = [];
    let sectionIndex = 0;
    this.template.instanceSections
      .filter(x => !(x.section.typeBw === SectionTypes.Dynamic && x.section.templateId))
      .forEach(instancesection => {
        sectionIndex += 1;
        let componentIndex = 1;
        stepList.push({
          instanceSection: instancesection,
          section: instancesection.section,
          sectionIndex: sectionIndex,
          sectionComponentsLength: instancesection.instanceSectionComponents.length ?? null,
          component: null,
          typeBw: instancesection.section.typeBw === SectionTypes.Dynamic ? StepType.AuthToolSection : StepType.TemplateSection,
        } as Step);
        instancesection.instanceSectionComponents.forEach(instanceSectionComp => {
          if (instanceSectionComp.component.componentType.name !== 'Banner' && instanceSectionComp.component.templateField?.isBuilderEnabled !== false) {
            stepList.push({
              instanceSection: instancesection,
              section: instancesection.section,
              instanceSectionComponentId: instanceSectionComp.id,
              sectionComponentsLength: instancesection.instanceSectionComponents.length ?? null,
              component: instanceSectionComp.component,
              componentIndex: componentIndex,
              typeBw: StepType.Component,
            } as Step);

            componentIndex += 1;
          }
        });
      });
    this.steps = stepList;

    this.setStep(this.editPanelBuilderIn?.id, scrollTo);
  }

  setSectionBuilder(section: ISection) {
    if (this.editPanelBuilderIn && this.editPanelBuilderIn.builderType === this.builderType.AddSection && this.editPanelBuilderIn.id === '') {
      this.addNewInstanceSection(section);
    }
  }

  subscribeToSectionChanges() {
    this.builderService.selectedSection$.pipe(takeUntil(this.componentDestroyed$)).subscribe(section => {
      if (section) {
        this.section = section;
      } else {
        this.closeMenu();
      }
    });
    this.builderService.sectionUpdated$.pipe(takeUntil(this.componentDestroyed$)).subscribe(section => {
      const index = this.template.instanceSections.findIndex(x => x.section.id === section.id);
      if (index !== -1) {
        this.template.instanceSections[index].section = section;
        this.createTemplateFormGroup();
      }
    });
  }

  addNewInstanceSection(section: ISection) {
    const maxSortOrder = Math.max(...this.template.instanceSections.map(x => x.sortOrder));
    const instanceSectionIn: IInstanceSectionIn = {
      instanceId: this.instance.id,
      templateId: this.template.id,
      sortOrder: maxSortOrder + 1,
      title: section.title ?? '',
      isHidden: false,
      backgroundColor: section.backgroundColor,
      masterSectionId: section.id,
    };
    if (instanceSectionIn) {
      this.dataService
        .addInstanceSection(instanceSectionIn)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(instanceSection => {
          if (instanceSection) {
            //SetTemplateFormControls.
            this.templateForm.addControl(
              instanceSection.id,
              new UntypedFormGroup({
                title: new UntypedFormControl(),
                description: new UntypedFormControl(),
                hideBackground: new UntypedFormControl(),
                sortOrder: new UntypedFormControl(),
                typeId: new UntypedFormControl(),
              })
            );

            //SetInstanceSection.
            const newInstanceSection: IInstanceSection = {
              id: instanceSection.id,
              sortOrder: 0,
              section: instanceSection.section,
              instanceSectionComponents: [],
              title: '',
              backgroundColor: instanceSection.backgroundColor,
              masterSectionId: instanceSection.masterSectionId,
            };

            if (instanceSection.instanceSectionComponents) {
              newInstanceSection.instanceSectionComponents = instanceSection.instanceSectionComponents;
            }

            this.template.instanceSections.push(newInstanceSection);
            this.setDirectNewSectionOrder(newInstanceSection);

            this.editPanelBuilderIn.id = instanceSection.section.id;
            this.editPanelBuilderIn.builderType = this.builderType.EditSection;
            this.editPanelBuilderIn.heading = 'Edit Section';

            this.updateInstanceSections();
          }
        });
    }
  }

  setDirectNewSectionOrder(newSection: IInstanceSection) {
    const previousIndex = this.editPanelBuilderIn.previousIndex ?? 0;
    const toIndex = previousIndex + 1;

    const index = this.template.instanceSections.indexOf(newSection);
    if (index !== -1) {
      const removeSection = this.template.instanceSections.splice(index, 1);
      this.template.instanceSections.splice(toIndex, 0, removeSection[0]);

      //NewSection.
      const selectedSectionAdd = this.template.instanceSections.find(x => x.id === newSection.id);
      if (selectedSectionAdd && selectedSectionAdd.section) {
        this.builderService.selectedSection$.next(selectedSectionAdd.section);
        this.editInstanceSectionsIn.push(selectedSectionAdd);
      }
    }

    //Set.
    this.template.instanceSections.forEach(sectionIn => {
      const newIndex = this.template.instanceSections.findIndex(x => x.id === sectionIn.id);
      sectionIn.sortOrder = newIndex as number;
    });
  }

  getStepIndexById(id: string): number {
    return this.steps.findIndex(x => (x.section.id === id && !x.component) || (x.component?.id === id && x.section.id !== id));
  }

  getMasterSection(sectionId: string) {
    const instanceSection = this.template.instanceSections.find(x => x.section.id === sectionId);

    return instanceSection?.masterSectionId
      ? this.template.instanceSections.find(x => x.section.id === instanceSection.masterSectionId)?.section
      : this.template.instanceSections.find(x => x.section.typeBw === SectionTypes.Dynamic && x.section.templateId)?.section;
  }

  persistEditPanelBuilder(index: number) {
    const step = this.steps[index];
    if (step) {
      this.editPanelBuilderIn.id = step?.component ? step.component.id : step.section?.id;
      this.editPanelBuilderChanged.next(null);
    }
  }

  setStep(id: string, scrollTo = false) {
    const index = this.getStepIndexById(id);
    if (index === -1) {
      return;
    }
    scrollTo = this.selectedIndex !== index;
    this.focusOnEditableStep(index);

    if (scrollTo === true) {
      this.setOnScroll(id);
    }
  }

  focusOnEditableStep(index: number) {
    this.selectedIndex = index;
  }

  setOnScroll(selectedId: string) {
    //There is somewhere a delay in the two panels.
    setTimeout(() => {
      const element = document.getElementById(`${selectedId}-left`) as HTMLElement;
      if (element) {
        element.scrollIntoView(false);
      }
    }, 1);
  }

  setIndexDirect() {
    const selectedIndex = this.selectedIndex;
    this.selectedIndex = this.stepper.selectedIndex;
    this.focusOnEditableStep(this.selectedIndex);

    if (selectedIndex !== this.stepper.selectedIndex) {
      this.save(this.steps[selectedIndex]);
    }

    this.persistEditPanelBuilder(this.selectedIndex >= 0 ? this.selectedIndex : 0);
  }

  goBack() {
    this.selectedIndex = this.stepper.selectedIndex - 1;
    this.persistEditPanelBuilder(this.selectedIndex >= 0 ? this.selectedIndex : 0);
  }

  goForward() {
    this.save(this.steps[this.selectedIndex]);
    if (this.selectedIndex === this.steps.length - 1 && this.editPanelBuilderIn.id !== '') {
      this.closeMenu();
      if (this.instance.feature.title == 'Classroom') {
        this.toast.presentToastWithOptions().then(result => {
          if (result == 'Open') {
            this.instanceService.openInstance('instance', this.instance.id, 'assignments', 'grid');
          }
        });
      }
      return;
    }

    this.selectedIndex = this.stepper.selectedIndex + 1;
    this.persistEditPanelBuilder(this.selectedIndex >= 0 ? this.selectedIndex : 0);
  }

  closeMenu() {
    this.editPanelBuilderIn.id = '';
    this.menuClosed.next(null);
  }

  createTemplateFormGroup() {
    const parentGroup: any = {};
    this.template.instanceSections.forEach(instanceSection => {
      const group: any = {};
      group['title'] = new UntypedFormControl(instanceSection.section?.title);
      group['description'] = new UntypedFormControl(instanceSection.section?.description);
      group['hideBackground'] = new UntypedFormControl(instanceSection.section?.hideBackground);
      group['sortOrder'] = new UntypedFormControl(instanceSection.section?.sortOrder);
      group['typeId'] = new UntypedFormControl(instanceSection.section?.typeId);
      instanceSection.instanceSectionComponents?.forEach(instanceSectionComponent => {
        group[instanceSectionComponent.component.id] = new UntypedFormControl(
          {
            value: this.getTemplateFieldValue(instanceSectionComponent) ?? '',
            disabled: instanceSectionComponent?.component?.templateField?.isBuilderEnabled === false,
          },
          instanceSectionComponent?.component?.templateField?.isRequiredField ? Validators.required : null
        );
      });
      parentGroup[instanceSection.section.id] = new UntypedFormGroup(group);
    });

    this.templateForm = new UntypedFormGroup(parentGroup);

    this.templateForm.valueChanges
      .pipe(
        debounceTime(5000) // Delay emissions by 5 seconds
      )
      .subscribe(value => {
        this.autosaveTriggered = true;
        this.unsavedChangesGuard.canDeactivateVar = false;
        this.save(this.steps[this.selectedIndex]);
      });
  }

  save(step: Step) {
    if (this.editPanelBuilderIn && this.editPanelBuilderIn.id) {
      if (step?.component && step?.section && step?.instanceSectionComponentId) {
        if (step?.component?.componentType?.parentTypeName === 'Question') {
          this.toast.presentToast('Step updated successfully!');
          this.checkShouldClose();
          return;
        }

        if (
          this.getFormControl(step?.section?.id, step?.component?.id).pristine &&
          step?.component?.componentType?.name !== 'Listing Details' &&
          step?.component?.componentType?.name !== 'Full Name' &&
          step?.component?.componentType?.name !== 'Phone Number' &&
          step?.component?.componentType?.name !== 'Email Chips' &&
          step?.component?.componentType?.name !== 'Address Search' &&
          step?.component?.componentType?.name !== 'External Html Block' &&
          step.component?.componentType?.name !== 'Question Builder' &&
          step?.component?.componentType?.name !== 'Icon & Dropdown'
        ) {
          this.checkShouldClose();
          return;
        }

        let instanceComponentsExist = false;
        let systemPropertiesExist = false;
        const requestList = [];
        const value = this.templateForm.get([step.section.id, step.component.id])?.value;

        if (value === undefined) {
          this.checkShouldClose();
          return;
        }
        if (step.component?.templateField?.systemProperty) {
          /* SYSTEM PROPERTY LOGIC */
          systemPropertiesExist = true;
          this.systemPropertyService.setSystemPropertyValue(step.component.templateField.systemProperty, value);
        } else {
          /* NON-SYSTEM PROPERTY LOGIC */
          if (step.component?.componentType?.name === 'Question Builder') {
            const question: IQuestionIn = {
              ...value,
              questionAnswers: value.questionAnswers.map((x: IQuestionAnswer) => ({ ...x, id: x.id !== '' ? x.id : null }) as IQuestionAnswerIn),
            } as IQuestionIn;

            this.systemPropertyService.setAssetSystemPropertyValueByKey('Question.QuestionTypeId', question.questionTypeId);
            this.systemPropertyService.setAssetSystemPropertyValueByKey('Question.QuestionText', question.questionText ?? '');
            this.systemPropertyService.setAssetSystemPropertyValueByKey('Question.QuestionDescription', question.questionDescription ?? '');

            if (question.isDynamicOptions && value.questionType.name.indexOf('Yes') === -1 && value.questionType.name.indexOf('True') === -1) {
              question.questionAnswers = question.questionAnswers.filter(x => x.isCorrect === true);
            }
            requestList.push(this.dataService.updateQuestion(question));
          } else if (step.component?.componentType?.name === 'Communication Manager') {
            //Values In String/Json Context (Type: Array/Multiple).
            const communicationBlocksIn = JSON.parse(value) as ICommunicationBlock[];
            requestList.push(this.dataService.updateAddCommunicationBlocks(communicationBlocksIn));
          } else if (step?.component?.templateField?.dropDownLinkType?.title === 'User Tags') {
            const tagIds = JSON.parse(value) as string[];
            requestList.push(this.dataService.updateRemoveUserTags(tagIds));
          } else if (step?.component?.templateField?.dropDownLinkType?.title === 'Campaign User Tags') {
            const tagIds = JSON.parse(value) as string[];
            requestList.push(this.dataService.updateCampaignTags(tagIds, this.instance.id));
          } else if (step.component?.componentType?.name !== 'Question Builder') {
            instanceComponentsExist = true;
          }
        }

        if (this.instance) {
          if (instanceComponentsExist && this.instance.feature?.featureType?.name !== 'Question Manager') {
            requestList.push(this.dataService.updateInstanceSectionComponent(step.instanceSectionComponentId, value));

            const request = this.updateTemplateField(step);
            if (request) {
              requestList.push(request);
            }
            const sectionIndex = this.template.instanceSections.findIndex(x => x.id === step.instanceSection.id);
            if (sectionIndex !== -1) {
              const instanceSectionComponentIndex = this.template.instanceSections[sectionIndex].instanceSectionComponents.findIndex(x => x.id === step.instanceSectionComponentId);
              this.template.instanceSections[sectionIndex].instanceSectionComponents[instanceSectionComponentIndex].value = value;
            }
          }

          if (
            systemPropertiesExist ||
            (step?.component?.componentType?.name === 'Listing Details' && (step?.component?.templateField.isInherit !== undefined ? step?.component?.templateField?.isInherit === true : true))
          ) {
            //DefaultInstanceProperties
            requestList.push(this.systemPropertyService.persistSystemPropertyValues(SystemPropertyType.Instance, this.instance.id));

            //CurrentDynamicProperties
            if (this.id) {
              const systemPropertyType = this.instance?.feature?.featureType?.systemPropertyType?.typeBw;
              if (systemPropertyType) {
                requestList.push(this.systemPropertyService.persistSystemPropertyValues(systemPropertyType, this.id));
              }
            } else if (this.instance.feature?.featureType?.systemPropertyType?.typeBw === SystemPropertyType.User) {
              //MyJourneyDefaultSlug
              requestList.push(this.systemPropertyService.persistSystemPropertyValues(SystemPropertyType.User, this.instance.id));
            }
          }

          if (this.instance.feature.featureType.name === 'Achievement Completion') {
            requestList.push(this.systemPropertyService.persistSystemPropertyValues(SystemPropertyType.CredentialEngineBadge, this.instance.id));
          }

          if (step?.component?.componentType?.name === 'Full Name' || step?.component?.componentType?.name === 'Phone Number' || step?.component?.componentType?.name === 'Email Chips') {
            requestList.push(this.systemPropertyService.persistSystemPropertyValues(SystemPropertyType.User, this.instance.id));
          }

          if (step?.component?.componentType?.name === 'Address Search') {
            if (this.instance.feature.featureType.name === 'Organization Manager') {
              requestList.push(this.systemPropertyService.persistSystemPropertyValues(SystemPropertyType.Organization, this.routeParams.instanceSlug ?? ''));
            } else if (
              this.instance.feature.featureType.name === 'Internal' ||
              this.instance.feature.featureType.name === 'Learning Objects' ||
              this.instance.feature.featureType.name === 'Marketing Objects' ||
              this.instance.feature.featureType.name === 'Training Objects' ||
              this.instance.feature.featureType.name === 'Landing Pages'
            ) {
              requestList.push(this.systemPropertyService.persistSystemPropertyValues(SystemPropertyType.Instance, this.instance.id));
            } else if (this.instance.feature.featureType.name === 'User Manager') {
              requestList.push(this.systemPropertyService.persistSystemPropertyValues(SystemPropertyType.User, this.instance.id));
            }
          }
        }

        if (requestList.length === 0) {
          this.checkShouldClose();
          return;
        }

        forkJoin(requestList)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(() => {
            this.toast.presentToast('Step updated successfully!');
            this.checkShouldClose();
          });
      }
    }
    this.unsavedChangesGuard.canDeactivateVar = true;
  }

  updateTemplateField(step: Step) {
    const component = step.instanceSection.instanceSectionComponents.find(x => x.id == step.instanceSectionComponentId)?.component;
    if (component?.templateField.stylingDirection || component?.templateField.darkText) {
      const updateTemplateField = this.dataService.updateTemplateField({
        ...component?.templateField,
        systemPropertyId: component?.templateField.systemProperty?.id,
        parentIdSystemPropertyLink: component?.templateField.parentIdSystemPropertyLink?.id,
        dropDownValues: component?.templateField.dropDownValues ? component?.templateField.dropDownValues.map(x => x.id) : null,
        dropDownLinkTypeId: component?.templateField?.dropDownLinkType?.id,
        communicationBlockId: component?.templateField?.communicationBlock?.id,
      } as ITemplateFieldIn);
      return updateTemplateField;
    }
    return null;
  }

  updateQuestionValue(question: IQuestion, step: Step) {
    this.dataService
      .updateQuestion(
        {
          ...question,
          questionTypeId: question.questionType.id,
          questionAnswers: question.isDynamicOptions
            ? question.questionAnswers.filter(x => x.isCorrect === true).map(x => ({ ...x, id: x.id !== '' ? x.id : null }) as IQuestionAnswerIn)
            : question.questionAnswers.map(x => ({ ...x, id: x.id !== '' ? x.id : null }) as IQuestionAnswerIn),
        } as IQuestionIn,
        this.instance.id
      )
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        const sectionIndex = this.template.instanceSections.findIndex(x => x.id === step.instanceSection.id);
        const componentIndex = this.template.instanceSections[sectionIndex].instanceSectionComponents.findIndex(x => x.id === step.instanceSectionComponentId);
        this.template.instanceSections[sectionIndex].instanceSectionComponents[componentIndex].component.question = { ...question } as IQuestion;
      });
  }

  checkShouldClose() {
    if (this.selectedIndex === this.steps.length) {
      this.closeMenu();
    }
  }

  getTemplateFieldValue(instanceSectionComponent: IInstanceSectionComponent) {
    if (!instanceSectionComponent?.component?.templateField?.systemProperty && instanceSectionComponent?.component?.templateField?.isInherit !== true) {
      return instanceSectionComponent?.value;
    } else if (instanceSectionComponent?.component?.templateField) {
      const value = this.systemPropertyService.getSystemPropertyValue(instanceSectionComponent?.component?.templateField?.systemProperty);
      return value;
    }

    return null;
  }

  getFormControl(sectionId: string, componentId: string | undefined): AbstractControl {
    return componentId && this.templateForm.get([sectionId, componentId]) ? (this.templateForm.get([sectionId, componentId]) as AbstractControl) : ({} as AbstractControl);
  }

  sectionComponentsUpdated(components: IInstanceSectionComponent[]) {
    const instanceSectionIndex = this.template.instanceSections.findIndex(x => x.id === this.editPanelBuilderIn.id);

    if (instanceSectionIndex !== -1) {
      const newComponent = components.find(x => this.template.instanceSections[instanceSectionIndex].instanceSectionComponents.findIndex(y => y.id === x.id) === -1);
      this.template.instanceSections[instanceSectionIndex].instanceSectionComponents = components;

      if (newComponent) {
        this.editPanelBuilderIn.id = newComponent?.component?.id;
      }
    }

    this.setupSteps(true);
    this.createTemplateFormGroup();
    this.editPanelBuilderIn.builderType = this.builderType.EditComponent;
  }

  componentChanged(refresh: boolean) {
    if (refresh === true) {
      this.forceChangeDetection();
    }
    this.editPanelBuilderIn.builderType = this.builderType.EditComponent;
  }

  editComponentSetup() {
    const step = this.steps[this.selectedIndex];
    this.editPanelBuilderIn.id = step.component?.id ?? '';
    this.editPanelBuilderIn.builderType = this.builderType.EditComponentSetup;
    this.editPanelBuilderIn.heading = 'Edit Component Setup';
  }

  deleteSection() {
    const step = this.steps[this.selectedIndex];
    this.alertService.presentAlert('Confirm Delete', 'Are you sure you want to delete this Section').then(() => {
      this.dataService
        .deleteInstanceSection(step.instanceSection.id)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(res => {
          if (res === true) {
            const index = this.template.instanceSections.findIndex(x => x.id === step.instanceSection.id);
            this.template.instanceSections.splice(index, 1);
            this.forceChangeDetection();
            this.persistEditPanelBuilder(this.selectedIndex - 1);
            this.setupSteps();
          }
        });
    });
  }

  deleteSectionComponent() {
    const step = this.steps[this.selectedIndex];
    this.alertService.presentAlert('Confirm Delete', 'Are you sure you want to delete this Component').then(() => {
      this.dataService
        .deleteInstanceSectionComponent(step.instanceSectionComponentId ?? '')
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(res => {
          if (res === true) {
            const sectionIndex = this.template.instanceSections.findIndex(x => x.id === step.instanceSection.id);
            const compIndex = this.template.instanceSections[sectionIndex].instanceSectionComponents.findIndex(x => x.id === step.instanceSectionComponentId);
            this.template.instanceSections[sectionIndex].instanceSectionComponents.splice(compIndex, 1);
            this.forceChangeDetection();
            this.persistEditPanelBuilder(this.selectedIndex - 1);
            this.setupSteps();
          }
        });
    });
  }

  moveSectionComponent(step: Step, upward: boolean) {
    if (this.template.instanceSections.length > 0) {
      //Find.
      const sectionIndex = this.template.instanceSections.findIndex(x => x.section.id === step.section.id);
      const compIndex = this.template.instanceSections[sectionIndex].instanceSectionComponents.findIndex(x => x.component.id === step.component?.id);
      const instanceSectionComponentsIn: IInstanceSectionComponentIn[] = [];
      const instanceSectionIds: string[] = [this.template.instanceSections[sectionIndex].id];

      const stepTo = this.steps[upward === true ? this.selectedIndex - 1 : this.selectedIndex + 1];
      const instanceSectionComponent = this.template.instanceSections[sectionIndex].instanceSectionComponents[compIndex];

      if (stepTo.typeBw === this.stepTypes.Component) {
        const stepToCompIndex = this.template.instanceSections[sectionIndex].instanceSectionComponents.findIndex(x => x.component.id === stepTo.component?.id);

        if (stepTo.component && stepTo.component?.builderRowNumber === instanceSectionComponent.component.builderRowNumber) {
          const currentColNum = instanceSectionComponent.component.templateField.colNumber;
          instanceSectionComponent.component.templateField.colNumber = stepTo.component?.templateField.colNumber;
          this.template.instanceSections[sectionIndex].instanceSectionComponents[stepToCompIndex].component.templateField.colNumber = currentColNum;
        } else {
          const currentRowNum = instanceSectionComponent.component.builderRowNumber;
          instanceSectionComponent.component.builderRowNumber = stepTo.component?.builderRowNumber;
          this.template.instanceSections[sectionIndex].instanceSectionComponents[stepToCompIndex].component.builderRowNumber = currentRowNum;
        }

        this.template.instanceSections[sectionIndex].instanceSectionComponents.splice(compIndex, 1);
        this.template.instanceSections[sectionIndex].instanceSectionComponents.splice(upward === true ? compIndex - 1 : compIndex + 1, 0, instanceSectionComponent);
      } else {
        const targetSectionIndex = upward === true ? sectionIndex - 1 : sectionIndex + 1;
        instanceSectionIds.push(this.template.instanceSections[targetSectionIndex].id);
        this.template.instanceSections[sectionIndex].instanceSectionComponents[compIndex].instanceSectionId = this.template.instanceSections[targetSectionIndex].id;
        this.template.instanceSections[sectionIndex].instanceSectionComponents.splice(compIndex, 1);

        const maxRowNum = Math.max(...this.template.instanceSections[targetSectionIndex].instanceSectionComponents.map(x => x.component.builderRowNumber ?? 0));
        instanceSectionComponent.component.builderRowNumber = maxRowNum + 1;
        instanceSectionComponent.component.templateField.colNumber = 0;

        if (upward !== true) {
          instanceSectionComponent.component.builderRowNumber = 0;

          this.template.instanceSections[targetSectionIndex].instanceSectionComponents.forEach(comp => (comp.component.builderRowNumber = (comp.component.builderRowNumber ?? 0) + 1));
        }

        this.template.instanceSections[targetSectionIndex].instanceSectionComponents.push(instanceSectionComponent);
      }

      //Set.
      this.template.instanceSections
        .filter(x => instanceSectionIds.indexOf(x.id) !== -1)
        .forEach(section => {
          instanceSectionComponentsIn.push(
            ...section.instanceSectionComponents.map(
              x =>
                ({
                  ...x,
                  colNumber: x.component.templateField.colNumber,
                  builderRowNumber: x.component.builderRowNumber,
                }) as IInstanceSectionComponentIn
            )
          );
        });

      if (compIndex !== -1) {
        this.updateInstanceSectionComponents(instanceSectionComponentsIn);
      }
    }
  }

  instanceSectionUpdated(title: any, instanceSectionId: string) {
    let index = this.template.instanceSections.findIndex(x => x.id === instanceSectionId);
    if (index !== -1) {
      this.template.instanceSections[index].title = title;
    }

    index = this.steps.findIndex(x => x.instanceSection.id === instanceSectionId);
    if (index !== -1) {
      this.steps[index].instanceSection.title = title;
    }

    this.forceChangeDetection();
  }

  hasAdminAccess() {
    return this.rolesService.hasFeatureRoleAccess([ActionTypes.Manage, ActionTypes.Publish]);
  }

  updateInstanceSectionComponents(instanceSectionComponents: IInstanceSectionComponentIn[]) {
    if (instanceSectionComponents?.length > 0) {
      this.dataService
        .updateInstanceSectionComponents(instanceSectionComponents)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(result => {
          if (result) {
            this.setupSteps();
          }
        });
    }
  }

  allowSectionComponentMoveUp(step: Step) {
    if (this.selectedIndex === 0 || !step || step?.typeBw === this.stepTypes.TemplateSection || step.component?.isLocked === true) {
      return false;
    }
    const prevStep = this.steps[this.selectedIndex - 1];
    const index = this.steps?.indexOf(step);

    // Check if the previous step is a section
    if (prevStep.component === null) {
      const stepAboveSection = this.steps[this.selectedIndex - 2];
      const prevSection = this.steps.find(x => x.instanceSection.id === stepAboveSection?.instanceSection?.id && x.component === null);

      if (prevSection) {
        return prevSection.typeBw === this.stepTypes.AuthToolSection && step.section.typeBw === SectionTypes.Dynamic;
      }
    }

    return index > 0 && step.section.typeBw === SectionTypes.Dynamic;
  }

  allowSectionComponentMoveDown(step: Step) {
    if (!step || step.typeBw === this.stepTypes.TemplateSection || step.component?.isLocked === true) {
      return false;
    }

    const nextStep = this.steps[this.selectedIndex + 1];

    // Check if the next step is a section
    if (nextStep?.component === null) {
      const stepBelowSection = this.steps[this.selectedIndex + 1];
      const nextSection = this.steps.find(x => x.instanceSection.id === stepBelowSection?.instanceSection?.id && x.component === null);

      if (nextSection) {
        return stepBelowSection.typeBw === this.stepTypes.AuthToolSection && step.section.typeBw === SectionTypes.Dynamic;
      }
    }

    return step.section.typeBw === SectionTypes.Dynamic;
  }

  allowSectionMoveUp(step: Step) {
    if (this.selectedIndex === 0) {
      return false;
    }

    const sectionSteps = this.steps.filter(x => x.typeBw !== StepType.Component);
    const index = sectionSteps?.indexOf(step);

    return index > 0 && step.typeBw === this.stepTypes.AuthToolSection;
  }

  allowSectionMoveDown(step: Step) {
    const sectionSteps = this.steps.filter(x => x.typeBw !== StepType.Component);
    const index = sectionSteps.indexOf(step);

    if (index === sectionSteps.length - 1) {
      return false;
    }

    return step.typeBw === this.stepTypes.AuthToolSection;
  }

  moveSection(section: ISection, upward: boolean) {
    if (this.template.instanceSections.length > 0) {
      //Find.
      const index = this.template.instanceSections.findIndex(x => x.section.id === section.id);
      const instanceSection = this.template.instanceSections[index];
      this.template.instanceSections.splice(index, 1);
      this.template.instanceSections.splice(upward === true ? index - 1 : index + 1, 0, instanceSection);

      //Set.
      this.template.instanceSections.forEach(sectionIn => {
        const newIndex = this.template.instanceSections.findIndex(x => x.id === sectionIn.id);
        sectionIn.sortOrder = newIndex as number;
      });

      if (section) {
        this.updateInstanceSections();
      }
    }
  }

  updateInstanceSections() {
    this.sortInstanceSections();
    this.instanceSectionsIn = this.template.instanceSections.map(
      x => ({ id: x.id, instanceId: this.instance.id, templateId: this.template.id, sortOrder: x.sortOrder, backgroundColor: x.backgroundColor }) as IInstanceSectionIn
    );
    if (this.instanceSectionsIn?.length > 0) {
      this.dataService
        .updateInstanceSections(this.instanceSectionsIn, true)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(result => {
          if (result) {
            this.setupSteps();
          }
        });
    }
  }

  toggleShowInstanceSection(instanceSection: IInstanceSection) {
    instanceSection.isHidden = !instanceSection.isHidden;
    const instanceSectionIn = { ...instanceSection, instanceId: this.instance.id, templateId: this.template.id, masterSectionId: null } as IInstanceSectionIn;
    this.dataService.updateInstanceSections([instanceSectionIn], false).pipe(takeUntil(this.componentDestroyed$)).subscribe();
  }

  updateInstanceSectionColor(backgroundColor: string | null, instanceSection: IInstanceSection) {
    instanceSection.backgroundColor = backgroundColor;
    const instanceSectionIn = { ...instanceSection, instanceId: this.instance.id, templateId: this.template.id, masterSectionId: null } as IInstanceSectionIn;
    this.dataService.updateInstanceSections([instanceSectionIn], false).pipe(takeUntil(this.componentDestroyed$)).subscribe();
  }

  sortInstanceSections() {
    this.template.instanceSections = this.template.instanceSections.sort((n1, n2) => n1.sortOrder - n2.sortOrder);
  }

  forceChangeDetection() {
    this.detectChanges.next(null);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
