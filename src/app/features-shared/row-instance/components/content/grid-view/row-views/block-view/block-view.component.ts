import { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, TemplateRef } from '@angular/core';
import { IContent } from '@app/core/contracts/contract';
import { IAspectRatio, RowService } from '@app/core/services/row.service';

@Component({
    selector: 'app-block-view',
    templateUrl: './block-view.component.html',
    styleUrls: ['./block-view.component.scss'],
    standalone: false
})
export class BlockViewComponent implements OnChanges {
  @Input() thumbnailTypesTemplate: TemplateRef<any>;
  @Input() aspectRatio: string;
  @Input() isDraggable: boolean;
  @Input() rowWidth: number;
  @Input() content: IContent[];
  @Input() rowLimit?: number;
  @Input() isEducator: boolean;
  @Input() hasAdminAccess: boolean;
  @Input() hideAddButton: boolean;
  @Output() contentOrderChanged: EventEmitter<IContent[]> = new EventEmitter<IContent[]>();
  @Output() contentMaxWidth = new EventEmitter();
  grid: Array<Array<IContent>>;
  maxWidth: string;
  rowData: IAspectRatio | undefined;
  rowIndex = 0;

  constructor(private rowService: RowService) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['aspectRatio'] || changes['content'] || changes['rowWidth'] || changes['rowLimit']) {
      this.rowData = this.rowService.setupVariables(this.aspectRatio, this.rowWidth);
      if (!this.rowData) return;
      this.maxWidth = (100 / this.rowData.columnCount).toString().concat('%');
      if (this.content) {
        this.buildGrid(this.rowData.columnCount);
        this.maxWidth = (100 / this.rowData.columnCount).toString().concat('%');
      }
    }
    if (changes['rowWidth']?.currentValue > 0) {
      this.contentMaxWidth.emit(this.rowWidth);
    }
  }

  buildGrid(itemsPerRow: number) {
    this.grid = this.content
      .map((e, i) => {
        return i % itemsPerRow === 0 ? this.content.slice(i, i + itemsPerRow) : [];
      })
      .filter(e => {
        return e;
      });
  }

  incrementNoneEmptyRow(row: IContent[], index: number) {
    if (index == 0) {
      this.rowIndex = 0;
    } else if (row.length > 0) {
      this.rowIndex++;
    }
    return this.rowIndex;
  }

  trackByFn(item: any): number {
    return item.id;
  }

  dropListDropped(event: any) {
    if (event.container === event.previousContainer) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);
    }

    // assign new ordering to content to rebuild/ remap grid
    this.content = []; // refresh content
    this.content = ([] as IContent[]).concat.apply([] as IContent[], this.grid);
    if (this.rowData) {
      this.buildGrid(this.rowData.columnCount);
    }
    this.contentOrderChanged.emit(this.content);
  }
}
