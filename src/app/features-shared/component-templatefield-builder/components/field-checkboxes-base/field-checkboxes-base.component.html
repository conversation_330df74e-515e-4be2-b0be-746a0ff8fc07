@if (baseForm) {
  <ng-container [formGroup]="baseForm">
    <mat-slide-toggle style="width: 100%" color="primary" formControlName="isHoverField">Visible on hover</mat-slide-toggle>
    <mat-slide-toggle style="width: 100%" color="primary" formControlName="isViewField">Visible on instance</mat-slide-toggle>
    <mat-slide-toggle style="width: 100%" color="primary" formControlName="isPreviewField">Preview in builder</mat-slide-toggle>
    <mat-slide-toggle style="width: 100%" color="primary" formControlName="isBuilderEnabled">Editable in builder</mat-slide-toggle>
    <mat-slide-toggle style="width: 100%" color="primary" formControlName="isRequiredField">Required – Section Completion</mat-slide-toggle>
    <mat-slide-toggle style="width: 100%" color="primary" formControlName="useMaxWidth">Use Max Section Width</mat-slide-toggle>
  </ng-container>
}
