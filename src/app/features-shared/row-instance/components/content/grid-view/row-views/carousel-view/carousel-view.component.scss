.swiper {
  overflow-y: visible !important;
  overflow-x: clip !important;
  width: 100%;
  overflow-y: -webkit-paged-x !important;
}

swiper-slide{
  padding: 2px;
  overflow: visible !important;
  margin: auto 0;
}

.swiper::part(container){
  overflow: visible !important
}

.swiper::part(pagination) {
  margin-top: 5px;
  bottom: auto !important;
  z-index: -1;
}

.swiper::part(bullet-active){
  background: #fff !important;
  width: 25px;
  height: 10px;
  border-radius: 10px;
}

.swiper::part(bullet) {
  background: #fff !important;
}

.parent-container {
  margin: 0px;
  margin-bottom: 15px;

  .custom-prev,
  .custom-next {
    display: flex;
    align-items: center;
    height: 100%;
    position: absolute;
    top: 45%;
    transform: translateY(-50%);
    cursor: pointer;
    z-index: 10;
    padding: 0px 10px 0px 20px;
  }

  @media screen and (max-width: 960px) {
    .custom-prev,
    .custom-next {
      display: none;
    }
  }

  .left-arrow {
    transform: rotate(180deg);
  }

  .icon {
    width: 20px;
    height: 40px;
  }

  .custom-prev {
    left: -45px;
  }

  .custom-next {
    right: -35px;
  }

  .swiper-slide {
    cursor: pointer;
  }
}
