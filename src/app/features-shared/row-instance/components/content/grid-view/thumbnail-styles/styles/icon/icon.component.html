<div class="layout" [ngClass]="status && (status === 'InProgress' ? 'status-inprogress' : status === 'Completed' ? 'status-complete' : null)" (click)="route()">
  <div class="icon-col-main icon-col">
    @if (content?.achievementCompletion) {
      <div class="gradient">
        <div (click)="openAchievementCompletionModalOnClick($event)" class="completion-header-container" loading="lazy">
          @if (badgeImageIcon) {
            <span class="badge-icon">
              <img ngSrc="{{ badgeImageIcon }}" fill="true" />
            </span>
          }
        </div>
      </div>
    }
    <img class="icon" ngSrc="{{ imageUrl }}" onerror="this.src='assets/images/no-image.png'" fill="true" />
  </div>
  <div class="text-container">
    <div class="tag">{{ tagName }}</div>
    @if (content.entityType === entityTypes.Instances && content.instanceDescriptors) {
      <h6 [innerHTML]="content.instanceDescriptors ?? '' | parsePipe: content.id : null : false | async | descriptorPipe: isMobile"></h6>
    } @else if (content.property && content.entityType === entityTypes.Organizations) {
      <h6>{{ content.property }} &#8226; {{ content.property2 }}</h6>
    } @else if (content.featureDescriptors) {
      <h6 [innerHTML]="content.featureDescriptors ?? '' | parsePipe: content.id : null : false | async | descriptorPipe: isMobile"></h6>
    } @else if (content.property2 && content.entityType !== entityTypes.Organizations) {
      <h6>{{ content.property2 }} Objects</h6>
    }
  </div>
  <app-thumbnail-icons
    [row]="row"
    [content]="content"
    [instance]="instance"
    [readingMode]="readingMode"
    [isDraggable]="isDraggable"
    [isEducator]="isEducator"
    [hasAdminAccess]="hasAdminAccess"
    [hideAddButton]="hideAddButton"
    [canHover]="false"
    [isAssignmentRow]="isAssignmentRow"
    (contentRemoved)="emitContentRemoved()"
    (editCustomRowContent)="editContent()">
  </app-thumbnail-icons>
</div>
@if (status) {
  <div class="status-foot-container">
    @if (status === 'InProgress') {
      <span class="inner" style="background-color: orange"> IN PROGRESS </span>
    }
    @if (status === 'Completed') {
      <span class="inner" style="background-color: green"> COMPLETED </span>
    }
  </div>
}
