.parent-layout {
  background-position: center;
  border-radius: 7px;
  background-size: cover;
  display: block;
  width: 100%;
  background-image: radial-gradient(rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 71%, rgba(0, 0, 0, 0.4) 100%), var(--background-image);
  margin-bottom: 15px;  

  .content-area {
    padding-bottom: 15px;
    padding-left: 30px;
    padding-right: 30px;
    padding-top: 40px;
    text-align: center;
    display: flex;   
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .parent-heading-container {
      .title {
        font-family: 'Roboto';
        font-size: 1.65em;      
        font-weight: 900;
        color: white;
        line-height: 1.1em;
        letter-spacing: 0.01em;
        padding: 0px;
        min-height: 30px;
      }

      .description {
        white-space: normal;
        word-wrap: break-word;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        max-height: 52px;
        padding-bottom: 7px !important;
        font-family: 'Roboto';
        font-size: 1.25em;
        font-weight: 400;
        color: #ccc;
        line-height: 1.3em;
        letter-spacing: 0.01em;      
      }
    }

    .description-hover{
      white-space: normal;
      display: -webkit-box;      
      -webkit-box-orient: vertical;      
      padding-bottom: 7px !important;
      font-family: 'Roboto';
      font-size: 1.25em;
      font-weight: 400;
      color: #ccc;
      line-height: 1.3em;
      letter-spacing: 0.01em;
    }

    .button-container {
      margin-top: 5px;
      padding: 0px;
      ion-button {
        --background: #ccc;
        font-weight: 700;
        font-family: 'Roboto';
        font-size: 1em;
        color: black;
        line-height: 1.1em;
        border-radius: 3px;
        letter-spacing: 0.01em;
      }

      @media screen and (max-width: 960px) {
        ion-button {
          min-height: 36px;
          --padding-top: 0px;
          --padding-bottom: 0px;
          --border-radius: 4px;
        }

        ion-button::part(native) {
          font-size: 16px;
        }
      }
    }
  }
}

.class-center {
  display: block !important;
  justify-content: center !important;
  text-align: center !important;

  .button-container {
    margin-top: 10px;
  }
}

.badge-icon {
  position: absolute;
  left: 0px;
  top: -12px;
  z-index: 999;
  width: 90px;
  height: 90px;
  border-radius: 5px;
}

.status-inprogress {
  outline: 2px solid orange;
  border-radius: 5px;
}

.status-complete {
  outline: 2px solid green;
  border-radius: 5px;
}

.status-foot-container {
  bottom: 0px;
}

.add-button-container {
  position: relative;
  display: flex;
  justify-content: flex-end;
}

.actions-heading {
  position: unset;
  z-index: 100;
}

.right-side {
  position: absolute;
  right: 5px;
  z-index: 999;
}

ion-button {
  --background: #fff;
}

.tracking {
  position: absolute;
  margin: auto;
  bottom: 5px;
  z-index: 999999;
  left: 20px;
  width: fit-content;
}

.lock-icon-circle-hover {
  margin-left: 11px;
}

.background-layout {
  background-size: cover !important;
}

.hover-item {
  outline: solid 1.5px #656565;
}
