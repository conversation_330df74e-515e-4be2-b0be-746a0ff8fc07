<ng-container [formGroup]="formGroup">
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isDynamicOptions">Dynamic options</mat-slide-toggle>
  @if (question.isDynamicOptions) {
    <div class="left-margin">
      @if (options$ | async; as options) {
        <app-select-option-control
          [options]="options"
          [label]="'Parent Tag'"
          [placeHolder]="'--select--'"
          [backgroundColor]="'#181818'"
          formControlName="dynamicOptionId"
          [toolTip]="'Choose the parent tag'"></app-select-option-control>
      }
    </div>
  }
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isAuthorEditable">Author editable</mat-slide-toggle>
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isRandom">Randomize</mat-slide-toggle>
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isOtherOption">"Other" option</mat-slide-toggle>
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isLimitTo">Limit to</mat-slide-toggle>
  @if (question.isLimitTo) {
    <app-text-input-control [formControlName]="'limit'" [type]="'number'"></app-text-input-control>
  }
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isOptionPhoto">Option photo</mat-slide-toggle>
  <div class="left-margin">
    @if (purposes) {
      <app-select-option-control
        [options]="purposes"
        [label]="'Purpose'"
        [placeHolder]="'--select--'"
        [backgroundColor]="'#181818'"
        formControlName="purpose"
        [toolTip]="'Choose the purpose'"></app-select-option-control>
    }
  </div>
  <app-text-input-control [formControlName]="'weight'" [label]="'Weight'" [type]="'number'" [limit]="4" [min]="0" [showSelected]="false"></app-text-input-control>
</ng-container>
