import { Directive, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { IContent, IEngagementIn, IInstance, IRouteParams, IRow } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { EngagementTypes } from '@app/core/enums/engagment-types.enum';
import { EntityTypes } from '@app/core/enums/entity-type';
import { ViewType } from '@app/core/enums/view-type';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { LayoutService } from '@app/core/services/layout-service';
import { AchievementCompletionModalComponent } from '@app/standalone/modals/achievement-completion-modal/achievement-completion-modal.component';
import { LockedModalComponent } from '@app/standalone/modals/locked-modal/locked-modal.component';
import { environment } from '@env/environment';
import { ModalController } from '@ionic/angular';
import { Subject } from 'rxjs';

@Directive()
export abstract class ThumbnailStyleBaseComponent implements OnDestroy {
  @Input() row: IRow;
  @Input() readingMode: boolean;
  @Input() content: IContent;
  @Input() routeParams: IRouteParams;
  @Input() instance: IInstance;
  @Input() isDraggable: boolean = false;

  @Output() selectedChanged = new EventEmitter();
  @Output() contentRemoved = new EventEmitter();
  @Output() editCustomRowContent = new EventEmitter<IContent>();

  componentDestroyed$: Subject<boolean> = new Subject();
  iconUrl: string;
  imageUrl: string;
  badgeImageIcon: string;
  badgeImageOverlay: string;
  actionTypes = ActionTypes;
  entityTypes = EntityTypes;

  constructor(
    public dataService: DataService,
    public authService: AuthService,
    private instanceService: InstanceService,
    private modalController: ModalController,
    public layoutService: LayoutService
  ) {}

  setCompletionAchievementUrls() {
    if (this.content?.achievementCompletion) {
      if (this.content.achievementCompletion.badgeIconImage) {
        this.badgeImageIcon = `${environment.contentUrl}asset/${this.content.achievementCompletion.badgeIconImage}/content?height=300`;
      } else {
        this.badgeImageIcon = 'assets/images/no-image.png';
      }

      if (this.content.achievementCompletion.badgeOverlayImage) {
        this.badgeImageOverlay = `${environment.contentUrl}asset/${this.content.achievementCompletion.badgeOverlayImage}/content?height=300`;
      } else {
        this.badgeImageOverlay = 'assets/images/defaultbackgroundgradient.png';
      }
    }
  }

  route() {
    sessionStorage.removeItem('peopleTableSelectedUserId');
    sessionStorage.removeItem('peopleTableSelectedUserFirstName');
    //ActionUrlRoute On ParentContainer Click.
    if (
      this.content.actionUrl &&
      (this.content?.actionBW >= ActionTypes.View || this.content.entityType === EntityTypes.DefaultInstance || (!this.authService.isGuest() && this.row.rowType.name === 'Custom') || (this.authService.isGuest() && this.row.allowGuestAccess))
    ) {
      if (this.content.actionUrl === 'player' && this.routeParams.viewType !== ViewType.Builder && this.routeParams.viewType !== ViewType.Player) {
        this.selectedChanged.emit(this.content.slug ?? this.content.id);
        return;
      } else if (this.content.actionUrl === 'player' && this.routeParams.viewType === ViewType.Player) {
        this.instanceService.openInstance(this.instance.slug ?? this.instance.id, this.content.id, null, 'player');
        return;
      } else if (this.content.actionUrl === 'player-direct' && this.routeParams.viewType !== ViewType.Builder) {
        this.instanceService.openInstance(this.content.id, null, null, 'player');
        return;
        // Duplicate IF statement ???
        // } else if (this.content.actionUrl === 'player' && this.routeParams.viewType === ViewType.Player) {
        //   this.instanceService.openInstance(this.routeParams.instanceSlug, this.content.id, null, 'player');
        //   return;
      }

      if (this.content.actionUrl.startsWith('http')) {
        if (this.layoutService.currentScreenSize === 'xs') {
          window.open(this.content.actionUrl, '_self')?.focus();
        } else if (this.content.sameUrlNavigation !== true) {
          window.open(this.content.actionUrl);
        } else {
          window.open(this.content.actionUrl, '_self');
        }
      } else if (this.content.entityType < 0) {
        this.instanceService.openInstance(this.content.actionUrl);
      } else if (this.content.entityType === EntityTypes.Instances || this.content.entityType === EntityTypes.DefaultInstance) {
        this.dataService.addInstanceEngagement({ instanceId: this.content.id, engagementType: EngagementTypes.Click, nominalValue: 1, percentageValue: 100 } as IEngagementIn).subscribe(() => {
          this.instanceService.openInstance(this.content.actionUrl, this.content.id);
        });
      } else {
        this.instanceService.openInstance(this.content.actionUrl, this.content.id, null, null, null, this.routeParams.viewType === ViewType.Player);
      }
    } else if (this.content.actionUrl) {
      this.openLockedModalOnClick();
    }
  }

  editContent() {
    this.editCustomRowContent.emit(this.content);
  }

  emitContentRemoved() {
    this.contentRemoved.emit();
  }

  async openLockedModalOnClick() {
    const modal = await this.modalController.create({
      component: LockedModalComponent,
      cssClass: 'completion-modal',
    });

    await modal.present();
  }

  async openAchievementCompletionModalOnClick(event: any) {
    event.stopPropagation();
    const modal = await this.modalController.create({
      component: AchievementCompletionModalComponent,
      componentProps: {
        achievementCompletion: this.content.achievementCompletion,
        assetImageIconUrl: this.iconUrl,
        badgeImageIcon: this.badgeImageIcon,
        badgeImageOverlay: this.badgeImageOverlay,
        instanceId: this.content.id,
      },
      cssClass: 'completion-modal',
    });

    await modal.present();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
