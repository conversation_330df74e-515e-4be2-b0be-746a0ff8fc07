:host {
  .row {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 11px;
  }
  
  .item {
    flex: 1;
    position: relative;
    margin-bottom: 3px;
  }

  .item:hover {
    cursor: pointer;
  }
}
.placeholder {
  position: relative;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

mat-icon {
  height: 27px !important;
}

.icon-container {
  display: flex;
  align-items: center;
}

.cdk-drag-handle {
  color: #cccccc;
  z-index: 2;
  cursor: move;
}
