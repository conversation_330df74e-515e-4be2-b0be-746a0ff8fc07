import { Component, Input, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { environment } from '@env/environment';
import { AuthService } from '@app/core/services/auth-service';
import { ThumbnailStyleBaseComponent } from '../../base/thumbnail-style-base.component';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-row-icon-item', // this selector does not fit the same convention as the others because 'app-row-icon' messes with angular
    templateUrl: './icon.component.html',
    styleUrls: ['./icon.component.scss'],
    standalone: false
})
export class IconComponent extends ThumbnailStyleBaseComponent implements OnInit {
  @Input() isEducator: boolean = false;
  @Input() hasAdminAccess: boolean = false;
  @Input() hideAddButton: boolean = false;
  @Input() status: string;
  @Input() tagName: string;
  @Input() property1: string;
  @Input() iconAssetId: string;
  @Input() externalLogoUrl?: string;
  @Input() isAssignmentRow?: boolean = false;
  isMobile: boolean = false;

  constructor(dataService: DataService, authService: AuthService, instanceService: InstanceService, modalController: ModalController, layoutService: LayoutService) {
    super(dataService, authService, instanceService, modalController, layoutService);
  }

  ngOnInit() {
    this.isMobile = this.layoutService.currentScreenSize === 'xs';
    this.setAssetUrls();
    this.setCompletionAchievementUrls();
    if (this.externalLogoUrl) {
      this.imageUrl = this.externalLogoUrl;
    } else if (this.iconAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.iconAssetId}/content?height=50`;
    } else {
      this.imageUrl = 'assets/images/no-image.png';
    }
  }

  setAssetUrls() {
    if (this.content.externalLogoUrl) {
      this.iconUrl = this.content.externalLogoUrl;
    } else if (this.content.iconAssetId) {
      this.iconUrl = `${environment.contentUrl}asset/${this.content.iconAssetId}/content?height=300`;
    } else {
      this.iconUrl = 'assets/images/no-image.png';
    }
  }
}
