@if (status === 'Completed') {
  <div class="tracking-container-row">
    <ion-grid>
      <ion-row class="ion-align-items-center">
        <ion-col class="time-outline-column" size="1">
          <ion-icon name="time-outline"></ion-icon>
        </ion-col>
        <ion-col class="text-column" size="11">
          <div class="text">Completed on {{ userInstanceTracking?.eventDate | date }}.</div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>
}

@if (status === 'InProgress') {
  <div class="tracking-container-row-uncomplete">
    <ion-grid>
      <ion-row class="ion-align-items-center">
        <ion-col class="time-outline-column" size="1">
          <ion-icon name="time-outline"></ion-icon>
        </ion-col>
        <ion-col class="text-column" size="11">
          <div class="text">Started on {{ userInstanceTracking?.eventDate | date }}.</div>
          @if ((userInstanceTracking?.itemsCompleted ?? 0) > 0 && (userInstanceTracking?.itemCount ?? 0) > 0) {
            <span>
              You've finished:
              <b
                ><span>{{ userInstanceTracking?.itemsCompleted }} of {{ userInstanceTracking?.itemCount }} items</span> | <span class="yellow-text">Continue your work</span></b
              ></span
            >
          }
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>
}
