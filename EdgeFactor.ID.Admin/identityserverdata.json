﻿{
    "IdentityServerData": {
        "IdentityResources": [
            {
                "Name": "roles",
                "Enabled": true,
                "DisplayName": "Roles",
                "UserClaims": [
                    "role"
                ]
            },
            {
                "Name": "openid",
                "Enabled": true,
                "Required": true,
                "DisplayName": "Your user identifier",
                "UserClaims": [
                    "sub"
                ]
            },
            {
                "Name": "profile",
                "Enabled": true,
                "DisplayName": "User profile",
                "Description": "Your user profile information (first name, last name, etc.)",
                "Emphasize": true,
                "UserClaims": [
                    "name",
                    "family_name",
                    "given_name",
                    "middle_name",
                    "nickname",
                    "preferred_username",
                    "profile",
                    "picture",
                    "website",
                    "gender",
                    "birthdate",
                    "zoneinfo",
                    "locale",
                    "updated_at"
                ]
            },
            {
                "Name": "email",
                "Enabled": true,
                "DisplayName": "Your email address",
                "Emphasize": true,
                "UserClaims": [
                    "email",
                    "email_verified"
                ]
            },
            {
                "Name": "address",
                "Enabled": true,
                "DisplayName": "Your address",
                "Emphasize": true,
                "UserClaims": [
                    "address"
                ]
            }
        ],
        "ApiScopes": [
            {
                "Name": "EFAdmin_api",
                "DisplayName": "EFAdmin_api",
                "Required": true,
                "UserClaims": [
                    "role",
                    "name"
                ]
            }
        ],
        "ApiResources": [
            {
                "Name": "EFAdmin_api",
                "Scopes": [
                    "EFAdmin_api"
                ]
            }
        ],
        "Clients": [
            {
                "ClientId": "EFAdmin",
                "ClientName": "EFAdmin",
                "ClientUri": "https://localhost:44303",
                "AllowedGrantTypes": [
                    "authorization_code"
                ],
                "RequirePkce": true,
                "ClientSecrets": [
                    {
                        "Value": "KM8d#W?EMpt7?j5"
                    }
                ],
                "RedirectUris": [
                    "https://localhost:44303/signin-oidc"
                ],
                "FrontChannelLogoutUri": "https://localhost:44303/signout-oidc",
                "PostLogoutRedirectUris": [
                    "https://localhost:44303/signout-callback-oidc"
                ],
                "AllowedCorsOrigins": [
                    "https://localhost:44303"
                ],
                "AllowedScopes": [
                    "openid",
                    "email",
                    "profile",
                    "roles"
                ]
            },
            {
                "ClientId": "EFAdmin_api_swaggerui",
                "ClientName": "EFAdmin_api_swaggerui",
                "AllowedGrantTypes": [
                    "authorization_code"
                ],
                "RequireClientSecret": false,
                "RequirePkce": true,
                "RedirectUris": [
                    "https://localhost:44302/swagger/oauth2-redirect.html"
                ],
                "AllowedScopes": [
                    "EFAdmin_api"
                ],
                "AllowedCorsOrigins": [
                    "https://localhost:44302"
                ]
            }
        ]
    }
}