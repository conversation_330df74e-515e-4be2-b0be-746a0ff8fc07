import { ContentQuillEditorComponent } from '@app/standalone/components/content-quill-editor/content-quill-editor.component';
import { SelectOptionControlComponent } from '@app/standalone/components/select-option-control/select-option-control.component';
import { TagSelectOptionControlComponent } from '@app/standalone/components/tag-select-option-control/tag-select-option-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { MatchingListSettingsComponent } from './components/matching-list-settings/matching-list-settings.component';
import { MultipleChoiceSettingsComponent } from './components/multiple-choice-settings/multiple-choice-settings.component';
import { OpinionScaleSettingsComponent } from './components/opinion-scale-settings/opinion-scale-settings.component';
import { PictureMatchingSettingsComponent } from './components/picture-matching-settings/picture-matching-settings.component';
import { QuestionBuilderComponent } from './components/question-builder/question-builder.component';
import { RiasecInterestProfilerSettingsComponent } from './components/riasec-interest-profiler-settings/riasec-interest-profiler-settings.component';
import { SettingsPanelComponent } from './components/settings-panel/settings-panel.component';
import { TextToPictureMatchingSettingsComponent } from './components/text-to-picture-matching-settings/text-to-picture-matching-settings.component';

export const featureComponents: any[] = [
  QuestionBuilderComponent,
  SettingsPanelComponent,
  MultipleChoiceSettingsComponent,
  OpinionScaleSettingsComponent,
  MatchingListSettingsComponent,
  PictureMatchingSettingsComponent,
  TextToPictureMatchingSettingsComponent,
  RiasecInterestProfilerSettingsComponent,
];

export const standaloneComponents: any[] = [SelectOptionControlComponent, TextInputControlComponent, TagSelectOptionControlComponent, ContentQuillEditorComponent];
