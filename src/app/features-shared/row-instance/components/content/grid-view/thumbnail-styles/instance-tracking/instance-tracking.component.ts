import { Component, Input, OnInit } from '@angular/core';
import { IUserInstanceTracking } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';

@Component({
    selector: 'app-instance-tracking',
    templateUrl: './instance-tracking.component.html',
    styleUrls: ['./instance-tracking.component.scss'],
    standalone: false
})
export class InstanceTrackingComponent implements OnInit {
  @Input() instanceId: string | null;
  @Input() status: string | undefined;
  userInstanceTracking: IUserInstanceTracking;

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.getUserInstanceTracking();
  }

  getUserInstanceTracking() {
    if (this.instanceId) {
      this.dataService.getUserInstanceTracking(this.instanceId).subscribe(data => {
        this.userInstanceTracking = data;
      });
    }
  }
}
