<div class="parent-container">
  <ion-row>
    <ion-col class="back-col" size="12">
      <ion-button fill="clear" size="small" color="warning" (click)="back()"> <ion-icon slot="start" name="chevron-back-outline"></ion-icon>Back</ion-button>
      <ion-button fill="solid" size="small" color="warning" (click)="save()">Add</ion-button>
    </ion-col>
  </ion-row>
  <ng-container>
    <div class="inner-search-container">
      <div class="top-heading-container">
        <ion-label class="heading" position="stacked"> Sections </ion-label>
        <br />
        <ion-label class="grey-text">Select sections to add</ion-label>
      </div>
      @if (sections) {
        <div class="dynamic-list-container">
          @for (section of sections; track section) {
            <div>
              <div class="component-container">
                <div [ngClass]="isSelected(section.id) ? 'component selected' : 'component'" [ngStyle]="{ 'background-color': '#111111' }" (click)="checkboxChanged(section)">
                  <div class="component-left">
                    <div class="inner-container">
                      <div class="heading">{{ section.title }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          }
        </div>
      }
    </div>
  </ng-container>
</div>
