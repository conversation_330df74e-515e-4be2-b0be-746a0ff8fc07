.field-container {
  padding: 16px;

  .heading-container {
    padding-bottom: 16px;
    ion-label {
      color: white;
    }

    .reqAsterisk {
      color: #7f550c;
      font-size: 20px;
    }
  }

  .tags-container {
    max-height: 300px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    background-color: #292929;
    border-radius: 4px;
    padding: 10px;

    .properties-container {
      width: 100%;
      display: flex;
      flex-direction: column;

      .property {
        height: 40px;
        width: 100%;
        border-radius: 5px;
        background-color: #111111;
        display: flex;
        flex-direction: row;
        align-items: center;
        align-content: center;
        justify-content: space-between;
        margin-bottom: 5px;
        border: 1px solid #111111;
        padding: 0px 10px 0px 10px;
        cursor: pointer;

        .property-left {
          display: flex;
          flex-direction: row;
          align-items: center;
          align-content: center;

          ion-radio {
            margin: 4px;
            min-width: 20px;
          }

          .propertyName {
            color: white;
            font-size: 14px;
            padding-left: 5px;
            padding-right: 5px;
            white-space: nowrap;
            width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .row {
          display: flex;
          flex-direction: row;
          align-items: center;
          align-content: center;

          ion-label {
            font-size: 12px;
            color: #909090;
          }

          ion-select {
            border: 1px solid #292929;
            border-radius: 5px;
            margin: 10px;
            padding: 3px;
            font-size: 12px;
            color: white;
          }
        }
      }
    }
  }
}
