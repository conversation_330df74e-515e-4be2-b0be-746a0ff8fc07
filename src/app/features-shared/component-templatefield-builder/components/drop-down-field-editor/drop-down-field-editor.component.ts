import { Component, EventEmitter, Input, OnChang<PERSON>, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent, IDropDownLinkType } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-drop-down-field-editor',
    templateUrl: './drop-down-field-editor.component.html',
    styleUrls: ['./drop-down-field-editor.component.scss'],
    standalone: false
})
export class DropDownFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  dropDownFieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;
  dropDownLinkTypes: KeyValue[];

  constructor(
    private formBuilder: UntypedFormBuilder,
    private dataService: DataService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.dropDownFieldForm.controls.isInherit;
  }

  get isParentSystemPropertyLinkControl(): AbstractControl {
    return this.dropDownFieldForm.controls.isParentSystemPropertyLinkField;
  }

  ngOnInit() {
    this.getDropDownLinkTypes();
  }

  getDropDownLinkTypes() {
    this.dataService
      .getDropDownLinkTypes()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.dropDownLinkTypes = data.map(x => ({ id: x.id, value: x.title }) as KeyValue);
        this.createForm();
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.dropDownFieldForm = this.formBuilder.group({
      dropDownLinkType: [this.component.templateField?.dropDownLinkType?.id, Validators.required],
      label: [this.component?.templateField?.label, Validators.required],
      placeholder: [this.component?.templateField?.placeHolderText, Validators.required],
      helpTitle: [this.component?.templateField?.helpTitle],
      helpDescription: [this.component?.templateField?.helpDescription],
      rowNumber: [this.component?.builderRowNumber],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isInherit: [this.component?.templateField?.isInherit ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      systemProperty: [this.component?.templateField?.systemProperty?.id],
      isFilter: [this.component?.templateField?.isFilter ?? false],
      isTag: [this.component?.templateField?.isTag ?? false],
      isParentSystemPropertyLinkField: [this.component?.templateField?.isParentSystemPropertyLinkField ?? false],
      parentIdSystemPropertyLink: [this.component?.templateField?.parentIdSystemPropertyLink?.id],
      tagTrees: [],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      isVariable: [this.component.templateField?.isVariable ?? false],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.dropDownFieldForm) {
      return;
    }

    this.dropDownFieldForm.controls.dropDownLinkType.setValue(this.component.templateField?.dropDownLinkType?.id);
    this.dropDownFieldForm.controls.label.setValue(this.component.templateField.label);
    this.dropDownFieldForm.controls.placeholder.setValue(this.component.templateField.placeHolderText);
    this.dropDownFieldForm.controls.helpTitle.setValue(this.component.templateField.helpTitle);
    this.dropDownFieldForm.controls.helpDescription.setValue(this.component.templateField.helpDescription);
    this.dropDownFieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.dropDownFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.dropDownFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.dropDownFieldForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.dropDownFieldForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.dropDownFieldForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.dropDownFieldForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.dropDownFieldForm.controls.isInherit.setValue(this.component.templateField.isInherit);
    this.dropDownFieldForm.controls.systemProperty.setValue(this.component.templateField.systemProperty);
    this.dropDownFieldForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.dropDownFieldForm.controls.isFilter.setValue(this.component.templateField.isFilter);
    this.dropDownFieldForm.controls.isTag.setValue(this.component.templateField.isTag);
    this.dropDownFieldForm.controls.parentIdSystemPropertyLink.setValue(this.component?.templateField?.parentIdSystemPropertyLink?.id);
    this.dropDownFieldForm.controls.isParentSystemPropertyLinkField.setValue(this.component?.templateField?.isParentSystemPropertyLinkField);
    this.dropDownFieldForm.controls.isVisibleRepository.setValue(this.component?.templateField?.isVisibleRepository);
    this.dropDownFieldForm.controls.isVariable.setValue(this.component?.templateField?.isVariable);
    this.dropDownFieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.dropDownFieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.dropDownFieldForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.dropDownFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.dropDownFieldForm.valid);
      this.setValues();
    });
  }

  tagSelectionChanged(tagChanged: boolean) {
    this.formValidityChanged.emit(tagChanged);
  }

  setValues() {
    if (this.dropDownFieldForm.valid) {
      this.component.templateField.dropDownLinkType = this.getDropDownLinkValue();
      this.component.templateField.label = this.dropDownFieldForm.controls.label.value;
      this.component.templateField.placeHolderText = this.dropDownFieldForm.controls.placeholder.value;
      this.component.templateField.helpTitle = this.dropDownFieldForm.controls.helpTitle.value;
      this.component.templateField.helpDescription = this.dropDownFieldForm.controls.helpDescription.value;
      this.component.builderRowNumber = this.dropDownFieldForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.dropDownFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.dropDownFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.dropDownFieldForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.dropDownFieldForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.dropDownFieldForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.dropDownFieldForm.controls.isHoverField.value;
      this.component.templateField.isInherit = this.dropDownFieldForm.controls.isInherit.value;
      this.component.templateField.isViewField = this.dropDownFieldForm.controls.isViewField.value;
      this.component.templateField.isFilter = this.dropDownFieldForm.controls.isFilter.value;
      this.component.templateField.isTag = this.dropDownFieldForm.controls.isTag.value;
      this.component.templateField.isParentSystemPropertyLinkField = this.dropDownFieldForm.controls.isParentSystemPropertyLinkField.value;
      this.component.templateField.isVisibleRepository = this.dropDownFieldForm.controls.isVisibleRepository.value;
      this.component.templateField.isVariable = this.dropDownFieldForm.controls.isVariable.value;
      this.component.templateField.colspan = this.dropDownFieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.dropDownFieldForm.controls.colNumber.value;
      this.component.templateField.useMaxWidth = this.dropDownFieldForm.controls.useMaxWidth.value;
    }
  }

  getDropDownLinkValue() {
    const link = this.dropDownLinkTypes.find(x => x.id === this.dropDownFieldForm?.controls?.dropDownLinkType?.value);

    return { id: link?.id, title: link?.value } as IDropDownLinkType;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
