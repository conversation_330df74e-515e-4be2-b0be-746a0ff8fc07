import { Component, EventEmitter, Input, OnChang<PERSON>, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, AbstractControl, Validators } from '@angular/forms';
import { IComponent, IDropDownLinkType } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-icon-base-field-editor',
    templateUrl: './icon-base-field-editor.component.html',
    styleUrls: ['./icon-base-field-editor.component.scss'],
    standalone: false
})
export class IconBaseFieldEditorComponent implements OnInit, OnChanges, OnD<PERSON>roy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  iconAndTextForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;
  dropDownLinkTypes: KeyValue[];

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService,
    private dataService: DataService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.iconAndTextForm.controls.isInherit;
  }

  get componentType() {
    return this.component.componentType.name;
  }

  ngOnInit() {
    if (this.componentType === 'Icon & Dropdown') {
      this.getDropDownLinkTypes();
    } else {
      this.createForm();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }

      this.setFormValues();
    }
  }

  getDropDownLinkTypes() {
    this.dataService
      .getDropDownLinkTypes()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.dropDownLinkTypes = data.map(x => ({ id: x.id, value: x.title }) as KeyValue);
        this.createForm();
      });
  }

  setTypeBwValues(typeBw: number | undefined) {
    if (typeBw !== undefined) {
      return this.builderService.acceptedImageFormats.filter(x => Number(x.id) & typeBw).map(x => x.id);
    }
    return null;
  }

  createForm() {
    this.iconAndTextForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      placeHolderText: [this.component?.templateField?.placeHolderText],
      caption: [this.component?.templateField?.helpTitle],
      description: [this.component?.templateField?.helpDescription],
      defaultText: [this.component?.templateField?.defaultText],
      defaultImageUrl: [this.component?.templateField?.defaultImageUrl],
      rowNumber: [this.component?.builderRowNumber ?? 0],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      isTag: [this.component?.templateField?.isTag ?? false],
      isInherit: [this.component?.templateField?.isInherit ?? false],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      systemProperty: [this.component?.templateField?.systemProperty?.id],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    if (this.componentType === 'Icon & Dropdown') {
      this.iconAndTextForm.addControl('dropDownLinkType', this.formBuilder.control(this.component.templateField?.dropDownLinkType?.id, Validators.required));
    }

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.iconAndTextForm) {
      return;
    }

    this.iconAndTextForm.controls.label.setValue(this.component.templateField.label);
    this.iconAndTextForm.controls.placeHolderText.setValue(this.component.templateField.placeHolderText);
    this.iconAndTextForm.controls.description.setValue(this.component.templateField.helpDescription);
    this.iconAndTextForm.controls.caption.setValue(this.component.templateField.helpTitle);
    this.iconAndTextForm.controls.defaultText.setValue(this.component.templateField.defaultText);
    this.iconAndTextForm.controls.defaultImageUrl.setValue(this.component.templateField.defaultImageUrl);
    this.iconAndTextForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.iconAndTextForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.iconAndTextForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.iconAndTextForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.iconAndTextForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.iconAndTextForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.iconAndTextForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.iconAndTextForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.iconAndTextForm.controls.isTag.setValue(this.component?.templateField?.isTag);
    this.iconAndTextForm.controls.isInherit.setValue(this.component.templateField.isInherit);
    this.iconAndTextForm.controls.systemProperty.setValue(this.component.templateField.systemProperty);
    this.iconAndTextForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.iconAndTextForm.controls.useMaxWidth.setValue(this.component.templateField.useMaxWidth);

    if (this.componentType === 'Icon & Dropdown') {
      this.iconAndTextForm.controls.dropDownLinkType.setValue(this.component.templateField?.dropDownLinkType?.id);
    }
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.iconAndTextForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.iconAndTextForm.valid);
      this.setValues();
    });
  }

  tagSelectionChanged(tagChanged: boolean) {
    this.formValidityChanged.emit(tagChanged);
  }

  setValues() {
    if (this.iconAndTextForm.valid) {
      this.component.templateField.label = this.iconAndTextForm.controls.label.value;
      this.component.templateField.placeHolderText = this.iconAndTextForm.controls.placeHolderText.value;
      this.component.templateField.helpDescription = this.iconAndTextForm.controls.description.value;
      this.component.templateField.helpTitle = this.iconAndTextForm.controls.caption.value;
      this.component.templateField.defaultText = this.iconAndTextForm.controls.defaultText.value;

      this.component.templateField.defaultImageUrl = this.iconAndTextForm.controls.defaultImageUrl.value;
      this.component.builderRowNumber = this.iconAndTextForm.controls.rowNumber.value;
      this.component.templateField.isRequiredField = this.iconAndTextForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.iconAndTextForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.iconAndTextForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.iconAndTextForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.iconAndTextForm.controls.isViewField.value;
      this.component.templateField.colspan = this.iconAndTextForm.controls.colspan.value;
      this.component.templateField.colNumber = this.iconAndTextForm.controls.colNumber.value;
      this.component.templateField.isTag = this.iconAndTextForm.controls.isTag.value;
      this.component.templateField.isInherit = this.iconAndTextForm.controls.isInherit.value;
      this.component.templateField.isVisibleRepository = this.iconAndTextForm.controls.isVisibleRepository.value;
      this.component.templateField.useMaxWidth = this.iconAndTextForm.controls.useMaxWidth.value;

      if (this.componentType === 'Icon & Dropdown') {
        this.component.templateField.dropDownLinkType = this.getDropDownLinkValue();
      }
    }
  }

  getDropDownLinkValue() {
    const link = this.dropDownLinkTypes.find(x => x.id === this.iconAndTextForm?.controls?.dropDownLinkType?.value);

    return { id: link?.id, title: link?.value } as IDropDownLinkType;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
