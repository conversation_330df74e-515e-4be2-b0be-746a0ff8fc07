<ng-container [formGroup]="formGroup">
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isDynamicOptions">Dynamic options</mat-slide-toggle>
  @if (question.isDynamicOptions) {
    @if (options$ | async; as options) {
      <app-select-option-control
        [options]="options"
        [label]="'Parent Tag'"
        [placeHolder]="'--select--'"
        [backgroundColor]="'#181818'"
        formControlName="dynamicOptionId"
        [toolTip]="'Choose the parent tag'"></app-select-option-control>
    }
  }
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isAuthorEditable">Author editable</mat-slide-toggle>
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isRandom">Randomize</mat-slide-toggle>
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isOtherOption">"Other" option</mat-slide-toggle>
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isLimitTo">Limit to</mat-slide-toggle>
  @if (question.isLimitTo) {
    <ion-input style="--background-color:{{ backgroundColor }};" formControlName="limit" type="number"></ion-input>
  }
  <mat-slide-toggle style="width: 100%" color="primary" formControlName="isOptionPhoto">Option photo</mat-slide-toggle>
  @if (purposes) {
    <app-select-option-control
      [options]="purposes"
      [label]="'Purpose'"
      [placeHolder]="'--select--'"
      [backgroundColor]="'#181818'"
      formControlName="purpose"
      [toolTip]="'Choose the purpose'"></app-select-option-control>
  }
</ng-container>
