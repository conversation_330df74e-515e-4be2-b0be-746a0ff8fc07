@if (pageBannerFieldForm) {
  <form [formGroup]="pageBannerFieldForm">
    <ion-grid>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Add field label...'" formControlName="label"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Placeholder Text'" [placeHolder]="'Add field placeholder...'" formControlName="placeHolderText"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Help Title'" [placeHolder]="'Add field help title...'" formControlName="helpTitle"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Help Description'" [placeHolder]="'Add field help description...'" formControlName="helpDescription"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Row Number'" [placeHolder]="'Add field row number...'" formControlName="rowNumber" [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Column Number (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add column number (Out of 12)...'"
            formControlName="colNumber"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Colspan (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add field colspan (Out of 12)...'"
            formControlName="colspan"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Hover Sort Order'"
            [placeHolder]="'Add field hover sort order...'"
            formControlName="hoverSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Instance Sort Order'"
            [placeHolder]="'Add field instance sort order...'"
            formControlName="instanceSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Aspect Ratio'"
            [placeHolder]="'Add banner aspect ratio...'"
            formControlName="aspectRatio"
            [type]="'string'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row class="file-size-parent-container">
        <ion-col size="12">
          <ion-item #control>
            <ion-row>
              <ion-col size="5">
                <app-text-input-control
                  [noPadding]="true"
                  [label]="'Min file size'"
                  [backgroundColor]="'#292929'"
                  [placeHolder]="'Kb'"
                  formControlName="minFileSize"
                  type="number"></app-text-input-control>
              </ion-col>
              <ion-col class="middle-col" size="2"> <h6>to</h6> </ion-col>
              <ion-col size="5">
                <app-text-input-control
                  [noPadding]="true"
                  [label]="'Max file size'"
                  [backgroundColor]="'#292929'"
                  [placeHolder]="'Mb'"
                  formControlName="maxFileSize"
                  type="number"></app-text-input-control>
              </ion-col>
            </ion-row>
          </ion-item>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-select-option-control
            [multiple]="true"
            [label]="'File Types'"
            [options]="builderService.acceptedHtml5Formats"
            [backgroundColor]="'#333333'"
            [disabled]="false"
            formControlName="fileTypeBw"></app-select-option-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-card>
            <ion-card-content>
              <app-field-checkboxes-base [baseForm]="pageBannerFieldForm"></app-field-checkboxes-base>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVisibleRepository">Is Visible in Repository</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isInherit">System Property</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="useMaxComponentWidth">Use Section Component Width</mat-slide-toggle>
              @if (isInheritControl.value) {
                <app-system-property-selector [templateField]="component.templateField" [formGroup]="pageBannerFieldForm"></app-system-property-selector>
              }
              <div class="default-image-container">
                <app-file-upload-control
                  [componentType]="component?.componentType?.name"
                  [fileTypeBw]="component?.templateField?.fileTypeBw"
                  formControlName="defaultImageUrl"
                  [label]="'Default File'"
                  [component]="component"
                  [toolTip]="'Default file'">
                </app-file-upload-control>
              </div>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
