:host {
  .builder-side-panel {
    background-color: #2d2e32;
    height: 100%;
    width: 100%;
    position: relative;

    .stepper-form-container {
      .inner-content-container {
        width: 100%;
        padding: 10px 10px 10px 20px;
        overflow-y: auto;
        .top-stepper-heading {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          .actions-container {
            display: flex;
            justify-content: center;
            align-items: center;
            .actions {
              ion-button {
                margin: 0px;
                cursor: pointer;
                ion-icon {
                  font-size: 20px;
                }
              }

              .close-button {
                ion-icon {
                  font-size: 25px;
                }
              }
            }
          }
        }

        .stepper-container {
          height: calc(100% - 10px);

          .header-row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            margin-bottom: -3px;

            .header {
              width: fit-content;
              background: #f99e00;
              color: #000000;
              border-color: #f99e00;
              border-width: 1px;
              border-style: solid;
              border-radius: 3px 3px 0px 0px;
              font-family: 'Roboto';
              font-weight: bold;
              line-height: 1.6;
              letter-spacing: 0.3px;
              text-align: left;
              height: 24px;
              z-index: 1000;
              font-size: 12px;
            }

            .title {
              padding: 2px 10px 16px 10px;
            }

            .buttons {
              display: flex;
              flex-direction: row;

              div {
                height: 23px;
                width: 23px;
                font-size: 16px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                align-content: center;
                cursor: pointer;
              }
            }
          }

          .step-component-container {
            margin-top: 2px;
            margin-bottom: 10px;
            border-radius: 3px;
          }
        }

        mat-stepper {
          background-color: transparent;
          position: sticky;
          top: 0;
          z-index: 1;
          ::ng-deep .mat-vertical-stepper-header {
            padding: 20px 0px 20px 10px;
            border-radius: 5px;
            height: fit-content;
            width: 100%;
          }

          ::ng-deep .mat-vertical-content-container {
            margin-left: 22px;
          }

          ::ng-deep .mat-step-icon-selected {
            border: 1px solid rgb(249, 158, 0) !important;
            color: rgb(249, 158, 0) !important;
            background-color: transparent;
          }

          ::ng-deep .mat-step-icon-content {
            font-family: 'Roboto';
            font-weight: 900;
            color: #fff;
          }

          ::ng-deep .mat-step-icon {
            border: 1px solid #6f6f6f;
            color: #6f6f6f;
            background-color: #2d2e32;
            margin-right: 10px;
            z-index: 2;
          }

          ::ng-deep .mat-vertical-content {
            padding: 0px 0px 10px 25px;
          }

          ::ng-deep mat-step-header[aria-selected='false'] ~ .mat-stepper-vertical-line::before {
            top: -28px;
            bottom: -28px;
            border-left: #555555 2px solid;
          }

          ::ng-deep mat-step-header[aria-selected='true'] ~ .mat-stepper-vertical-line::before {
            top: -90px;
            bottom: -45px;
            border-left: 2px solid transparent;
            border-image: linear-gradient(to bottom, transparent 0%, rgb(249, 158, 0) 20%, transparent 90%) !important;
            border-image-slice: 1 !important;
          }

          ::ng-deep .mat-step-label {
            width: 100%;
          }

          ::ng-deep .mat-step-text-label {
            font-size: 18px;
            color: rgb(148, 148, 148);
          }
        }
      }

      .stepper-footer {
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 29%, rgba(0, 0, 0) 53%, rgba(0, 0, 0) 100%);
        left: 0;
        right: 0;
        width: 100%;
        bottom: 0px;
        margin: auto;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .next-button {
          margin-right: 10px;
          --background: #f99e00;
          .inner-container {
            text-transform: none;
            color: #0000;
            border-radius: 3px;
            font-family: 'Roboto';
            font-weight: 500;
            font-size: 1.125em;
            line-height: 1;
            text-align: center;
            letter-spacing: 0.1em;
          }
        }

        @media screen and (max-width: 960px) {
          .next-button ion-button {
            --border-radius: 4px;
            min-height: 36px;
            --padding-top: 0px;
            --padding-bottom: 0px;
          }
        }

        .back-button {
          margin-left: 10px;
          .inner-container {
            --padding-start: 0 !important;
            text-transform: none;
            color: #aaa;
            border-radius: 3px;
            font-weight: 500;
            font-size: 18px;
            text-decoration: underline;
            line-height: 1;
            letter-spacing: 0.1em;
            mat-icon {
              width: 24px;
              height: 24px;
              padding-left: 5px;
              margin-right: 18px;
              color: #fff;
            }
          }
        }
        
        @media screen and (max-width: 960px) {
          .back-button ion-button {
            --border-radius: 4px;
            min-height: 36px;
            --padding-top: 0px;
            --padding-bottom: 0px;
          }
        }
      }

      @media (max-width: 960px) {
        .stepper-footer {
          justify-content: center;
        }
      }
    }

    .static-container {
      padding: 10px 10px 0px 20px;
      position: relative;
      height: 100%;
    }
  }

  .section-container {
    margin-top: 2px;
    margin-bottom: 10px;
    border-radius: 3px;
    padding: 10px;
  }

  .selected {
    border: 1px solid rgb(249, 158, 0);
  }

  .text-container {
    display: flex;
    align-items: center;
    .text {
      color: #aaaaaa;
      font-family: 'Roboto';
      font-weight: 900;
      font-size: 14px;
      font-style: italic;
      letter-spacing: 0.5px;
      text-align: left;
    }
  }
  .text-and-icon-container {
    display: flex;
    justify-content: space-between;

    .icon-container {
      color: white;
      font-size: 20px;
      margin: 0 10px;
      align-items: center;
      display: flex;
    }
  }
}
