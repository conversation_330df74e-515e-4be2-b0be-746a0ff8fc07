﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AskForPersonalDataLog" xml:space="preserve">
    <value>Användaren med id '{0}' har efterfrågat sina personuppgifter.</value>
  </data>
  <data name="AuthenticatorVerified" xml:space="preserve">
    <value>Din autentiserings applikation har verifierats.</value>
  </data>
  <data name="ConfirmEmailBody" xml:space="preserve">
    <value>Verifiera ditt konto genom att &lt;a href='{0}'&gt;klicka här&lt;/a&gt;.</value>
  </data>
  <data name="ConfirmEmailTitle" xml:space="preserve">
    <value>Verifiera din e-postadress</value>
  </data>
  <data name="DeletePersonalData" xml:space="preserve">
    <value>Användaren med id '{0}' har raderat sitt konto.</value>
  </data>
  <data name="Error2FANotEnabled" xml:space="preserve">
    <value>Kan inte generera återställningskoder till användaren med ID {0} för att de inte aktiverat tvåfaktorsautentisering.</value>
  </data>
  <data name="ErrorCode" xml:space="preserve">
    <value>Felkod</value>
  </data>
  <data name="ErrorDeletingUser" xml:space="preserve">
    <value>Oväntat fel inträffade när användaren med id {0} skulle raderas.</value>
  </data>
  <data name="ErrorDisable2FA" xml:space="preserve">
    <value>Oväntat fel inträffade när tvåfaktorsautentisering skulle inaktiveras för användare med ID {0}.</value>
  </data>
  <data name="ErrorGenerateCodesWithout2FA" xml:space="preserve">
    <value>Kan inte generera återställningskoder för att användaren inte har tvåfaktorsautentisering aktiverat.</value>
  </data>
  <data name="ErrorLoadingExternalLogin" xml:space="preserve">
    <value>Oväntatfel inträffade när information om den externa inloggningen till användaren med ID {0} skulle laddas.</value>
  </data>
  <data name="ErrorRemovingExternalLogin" xml:space="preserve">
    <value>Oväntat fel inträffade när den externa inloggningen till användar med ID {0} skulle tas bort.</value>
  </data>
  <data name="ErrorSettingEmail" xml:space="preserve">
    <value>Oväntat fel inträffade när e-post addressen till användare med ID {0} skulle sparas.</value>
  </data>
  <data name="ErrorSettingPhone" xml:space="preserve">
    <value>Oväntat fel inträffade när telefonnummer till användare med ID {0} skulle sparas.</value>
  </data>
  <data name="ExternalLoginAdded" xml:space="preserve">
    <value>Den extarna inloggningen har lagts till.</value>
  </data>
  <data name="ExternalLoginRemoved" xml:space="preserve">
    <value>Den externa inloggningen har tagitsbort.</value>
  </data>
  <data name="InvalidVerificationCode" xml:space="preserve">
    <value>Verifieringskoden är felaktig.</value>
  </data>
  <data name="PasswordChanged" xml:space="preserve">
    <value>Ditt lösenord har ändrats.</value>
  </data>
  <data name="PasswordChangedLog" xml:space="preserve">
    <value>Användaren {0} har ändrat sitt lösenord.</value>
  </data>
  <data name="PasswordNotCorrect" xml:space="preserve">
    <value>Lösenordet är felaktigt.</value>
  </data>
  <data name="PasswordSet" xml:space="preserve">
    <value>Ditt lösenord har sparats.</value>
  </data>
  <data name="ProfileUpdated" xml:space="preserve">
    <value>Din profil har uppdaterats</value>
  </data>
  <data name="SuccessDisabled2FA" xml:space="preserve">
    <value>Användaren med ID {0} har inaktiverat tvåfaktorsautnetisering.</value>
  </data>
  <data name="SuccessForgetBrowser2FA" xml:space="preserve">
    <value>Denna webbläsaren är nu bort glömd. Nästa gång du loggar in med denna webbläsare är du tvungen att logga in med tvåfaktorsautentisering igen.</value>
  </data>
  <data name="SuccessResetAuthenticationKey" xml:space="preserve">
    <value>Användare med ID {0} har resetat sig authentiseringapp nyckel.</value>
  </data>
  <data name="SuccessUserEnabled2FA" xml:space="preserve">
    <value>Användare med ID {0} har aktiverat tvåfaktorsautentisering via en autentiseringsapp.</value>
  </data>
  <data name="UserGenerated2FACodes" xml:space="preserve">
    <value>Användare med ID {0} har genererat nya tvåfaktorsautentiseringsåterställningskoder.</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>Kunde inte ladda använare med ID {0}.</value>
  </data>
  <data name="VerificationSent" xml:space="preserve">
    <value>Verifierings p-post har skickats. Vänligen kontrollera din e-post.</value>
  </data>
</root>