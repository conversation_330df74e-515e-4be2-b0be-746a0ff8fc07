<div class="custom-content-container">
  @if (customContentForm) {
    <form [formGroup]="customContentForm">
      <ion-grid>
        <ion-row class="top-row">
          <ion-col style="display: flex; justify-content: flex-start">
            <mat-icon (click)="cancel()">cancel</mat-icon>
          </ion-col>
          <ion-col style="display: flex; justify-content: flex-end">
            @if (customItem) {
              <ion-button style="margin-right: 10px" (click)="deleteContent()" color="primary">Remove</ion-button>
            }
            @if (customContentForm?.valid) {
              <ion-button (click)="saveContent()" color="primary">SAVE</ion-button>
            }
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control [toolTip]="'The name of your content block'" [placeHolder]="'Add a content name here...'" [label]="'Title'" [formControlName]="'title'" [itemBackgroundColor]="''">
            </app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control
              [toolTip]="'The description of your content block'"
              [placeHolder]="'Add a description of your content here...'"
              [label]="'Description'"
              [formControlName]="'description'"
              [itemBackgroundColor]="''">
            </app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-file-upload-control [label]="'Background Image'" [formControlName]="'backgroundImageAssetId'" [fileFormat]="'/*/'" [itemBackgroundColor]="''"></app-file-upload-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control
              [toolTip]="'The text on the button'"
              [placeHolder]="'Add button label here...'"
              [label]="'Button Label'"
              [formControlName]="'buttonText'"
              [itemBackgroundColor]="''">
            </app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control
              [toolTip]="'The url that the button click will take the user to'"
              [placeHolder]="'Add button Url here...'"
              [label]="'Button Url'"
              [formControlName]="'buttonUrl'"
              [itemBackgroundColor]="''">
            </app-text-input-control>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="sameUrlNavigation">Navigate on current page</mat-slide-toggle>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control
              [backgroundColor]="'#292929'"
              [label]="'Order'"
              [placeHolder]="'1'"
              formControlName="rowNumber"
              [type]="'number'"
              [formControlName]="'contentOrder'"
              [itemBackgroundColor]="''">
            </app-text-input-control>
          </ion-col>
        </ion-row>
      </ion-grid>
    </form>
  }
</div>
