@if (rowEditorForm && formLoaded === true) {
  <ng-container [formGroup]="rowEditorForm">
    <ion-card color="dark">
      <ion-radio-group formControlName="rowType">
        @for (type of rowTypes; track type) {
          <ion-item>
            <ion-label>{{ type.name }}</ion-label>
            <ion-radio [value]="type.typeBw" (click)="setRowType($event)"></ion-radio>
          </ion-item>
        }
      </ion-radio-group>
    </ion-card>
    <ion-row>
      <ion-col>
        <app-text-input-control [noPadding]="false" [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Add field label...'" formControlName="label"></app-text-input-control>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <app-text-input-control
          [noPadding]="false"
          [backgroundColor]="'#292929'"
          [label]="'Description'"
          [placeHolder]="'Add field description...'"
          formControlName="description"></app-text-input-control>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <app-text-input-control [noPadding]="false" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Add field caption...'" formControlName="caption"> </app-text-input-control>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <app-text-input-control [backgroundColor]="'#292929'" [label]="'Row Number'" [placeHolder]="'Add field row number...'" formControlName="rowNumber" [type]="'number'"> </app-text-input-control>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <app-text-input-control
          [backgroundColor]="'#292929'"
          [label]="'Column Number (Out of 12)'"
          [limit]="12"
          [placeHolder]="'Add column number (Out of 12)...'"
          formControlName="colNumber"
          [type]="'number'"></app-text-input-control>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <app-text-input-control
          [backgroundColor]="'#292929'"
          [label]="'Colspan (Out of 12)'"
          [limit]="12"
          [placeHolder]="'Add field colspan...'"
          formControlName="colspan"
          [type]="'number'"></app-text-input-control>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <mat-slide-toggle style="width: 100%" color="primary" formControlName="useMaxWidth">Use Max Section Width</mat-slide-toggle>
      </ion-col>
    </ion-row>
  </ng-container>
}
