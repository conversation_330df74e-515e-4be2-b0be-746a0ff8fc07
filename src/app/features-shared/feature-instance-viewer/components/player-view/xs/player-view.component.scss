.parent-container {
  height: 100%;

  .player-row-flex {
    display: flex;
    flex-direction: row;
    background-color: transparent;
    height: 100%;
    overflow: hidden;

    mat-sidenav {
      width: 80%;
      background-color: #292929;
      overflow-y: auto;
      height: calc(100vh - 270px);

      .player-side-panel-container {
        background-color: #292929;
      }
    }

    .player-width {
      width: 100%;
      overflow: hidden;
      position: relative;

      .player-inner-container {
        width: 100%;
        float: left;
        height: 100%;

        .content-wrapper {
          height: 100%;
          overflow: auto;
        }

        .player-view-info-container {
          width: 100%;
          height: 100%;
          margin-top: 5px;
        }

        .player-outer-container {
          margin-right: var(--page-margin-right-player);
          margin-left: var(--page-margin-left-player);
          height: calc(100vh - 300px);

          .player-container {
            border: 2px solid rgba(249, 158, 0, 0.4);
            background-color: #232323;
            border-radius: 13px 13px 15px 15px;
            margin-bottom: 20px;
            overflow: hidden;
            height: 100%;
          }
        }

        .toast-visible {
          padding-bottom: 80px;
        }

        .searchBar {
          padding-bottom: 80px;
        }

        .searchBar.toast-visible {
          padding-bottom: 120px;
        }
      }
    }
  }
}

@media screen and (min-width: 760px) and (max-width: 992px) {
  .player-row-flex {
    display: flex;
    flex-direction: row;
    height: 100%;

    .player-width {
      width: 100%;
      position: relative;

      .player-inner-container {
        width: 100%;
        float: left;
        height: 100%;

        .top-border {
          border-color: #171717;
          border-width: 1px;
          border-style: solid;
          width: 100%;
        }

        .content-wrapper {
          height: 100%;
        }

        .player-view-info-container {
          width: 100%;
          overflow-y: scroll;
          height: calc(100vh - 250px);
        }

        .custom-info-height {
          height: calc(100vh - 220px) !important;
        }

        @media screen and (max-width: 960px) {
          .custom-info-height {
            height: calc(100vh - 240px) !important;
          }
        }

        .player-outer-container {
          margin: auto;
          padding-left: 3.5vw;
          padding-right: 2.5vw;
          height: 100%;
          overflow: hidden;

          .player-border {
            background-color: #232323;
            border-radius: 13px 13px 15px 15px;
            margin-bottom: 20px;
            overflow: hidden;
          }

          .player-container {
            border: 2px solid rgba(249, 158, 0, 0.4);
          }

          .player-container-completed {
            border: 2px solid green;
            margin-top: 10px;
          }

          .player-container-in-progress {
            border: 2px solid #606060;
            margin-top: 10px;
          }

          .player-container-incomplete {
            border: 2px solid #5f2524;
            margin-top: 10px;
          }
        }

        @media screen and (min-width: 993px) and (max-width: 1250px) {
          .player-outer-container {
            padding-left: 30px;
            padding-right: 30px;
          }
        }

        @media screen and (min-width: 769px) and (max-width: 992px) {
          .player-outer-container {
            padding-left: 20px;
            padding-right: 25px;
          }
        }

        @media screen and (max-width: 769px) {
          .player-outer-container {
            padding-left: 0;
            padding-right: 0;
          }
        }

        .toast-visible {
          height: calc(100vh - 310px) !important;
        }

        .searchBar {
          height: calc(100vh - 310px) !important;
        }

        .searchBar.toast-visible {
          height: calc(100vh - 370px) !important;
        }
      }
    }

    .completed-header-container {
      ion-row {
        z-index: 1006;
        position: absolute;
        top: 0px;
        left: calc(50% - 36px);

        .inner-completed {
          background-color: green;
          padding: 2px;
          font-size: 12px;
          border-radius: 5px;
          color: white;
          margin-right: 16px;
        }

        .inner-in-progress {
          background-color: #ac7f00;
          padding: 2px;
          font-size: 12px;
          border-radius: 5px;
          color: white;
          margin-right: 16px;
        }

        .inner-incomplete {
          background-color: red;
          padding: 2px;
          font-size: 12px;
          border-radius: 5px;
          color: white;
          margin-right: 16px;
        }
      }
    }

    @media screen and (min-width: 769px) and (max-width: 992px) {
      .player-side-panel-container {
        width: clamp(400px, 25%, 575px);
        overflow-y: auto;
        height: calc(100vh - 230px);
        background-color: #292929;
      }

      .player-outer-container {
        padding-left: 20px;
        padding-right: 25px;
      }
    }

    @media screen and (max-width: 769px) {
      .player-outer-container {
        padding-left: 0px;
        padding-right: 0px;
      }
    }
  }

  .searchBar {
    height: calc(100vh - 210px);
  }
}
