import { Component, Input, OnInit } from '@angular/core';
import { environment } from '@env/environment';
import { ThumbnailStyleBaseComponent } from '../base/thumbnail-style-base.component';

@Component({
    selector: 'app-row-badge-container',
    templateUrl: './row-badge-container.component.html',
    styleUrls: ['./row-badge-container.component.scss'],
    standalone: false
})
export class RowBadgeContainerComponent extends ThumbnailStyleBaseComponent implements OnInit {
  @Input() entityType: number;
  @Input() featureDescriptor: string;
  @Input() instanceName: string;
  @Input() organizationName: string;
  @Input() description: string;
  @Input() iconAssetId: string;
  @Input() progress: number;
  @Input() status: string;
  @Input() lastModifiedDate: string;
  @Input() isEnrolled: boolean;
  @Input() isEducator: boolean;
  @Input() hasAdminAccess: boolean;
  @Input() hideAddButton: boolean;
  setLastModifiedDate = new Date();

  ngOnInit() {
    this.setData();
    this.setImageIcon();
  }

  setData() {
    if (this.lastModifiedDate) {
      this.setLastModifiedDate = new Date(this.lastModifiedDate);
    }
  }

  setImageIcon() {
    if (this.iconAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.iconAssetId}/content`;
    } else {
      this.imageUrl = 'assets/images/no-image.png';
    }
  }
}
