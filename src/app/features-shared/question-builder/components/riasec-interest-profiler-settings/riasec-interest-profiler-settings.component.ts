import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { IQuestion } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { Observable, map } from 'rxjs';

@Component({
    selector: 'app-riasec-interest-profiler-settings',
    templateUrl: './riasec-interest-profiler-settings.component.html',
    styleUrls: ['./riasec-interest-profiler-settings.component.scss'],
    standalone: false
})
export class RiasecInterestProfilerSettingsComponent implements OnInit {
  @Input() question: IQuestion;
  @Input() backgroundColor = '#181818';
  @Output() questionChanged = new EventEmitter<any>();
  formGroup: UntypedFormGroup;
  maxLength = 24;
  options$: Observable<KeyValue[]>;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.initData();
    this.setupForm();
  }

  initData() {
    this.options$ = this.dataService.getTagChildren('9CF13D99-F5EF-4437-9A52-77A239CA4C76').pipe(
      map(tags => {
        return tags.map(t => {
          return { id: t.id, value: t.name } as KeyValue;
        });
      })
    );
  }

  setupForm() {
    this.formGroup = this.formBuilder.group({
      dynamicOptionId: [this.question?.dynamicOptionsParentId],
    });

    this.subscribeToForm();
  }

  subscribeToForm() {
    this.formGroup.valueChanges.subscribe(() => {
      this.question.dynamicOptionsParentId = this.formGroup.controls.dynamicOptionId.value;

      this.questionChanged.emit(null);
    });
  }
}
