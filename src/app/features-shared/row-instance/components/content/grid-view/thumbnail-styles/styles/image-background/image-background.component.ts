import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { IRouteParams } from '@app/core/contracts/contract';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { LayoutService } from '@app/core/services/layout-service';
import { ScormService } from '@app/core/services/scorm.service';
import { RowContentHoverValues } from '@app/shared/directives/row-content-hover.directive';
import { environment } from '@env/environment';
import { ModalController, Platform } from '@ionic/angular';
import { takeUntil } from 'rxjs';
import { ThumbnailStyleBaseComponent } from '../../base/thumbnail-style-base.component';

export interface OverlayEventDetail<T = any> {
  data?: T;
  role?: string;
}
@Component({
    selector: 'app-row-image-background',
    templateUrl: './image-background.component.html',
    styleUrls: ['./image-background.component.scss'],
    standalone: false
})
export class ImageBackgroundComponent extends ThumbnailStyleBaseComponent implements OnInit, OnDestroy {
  @Input() thumbnailType: string;
  @Input() isEducator: boolean = false;
  @Input() hasAdminAccess: boolean = false;
  @Input() hideAddButton: boolean = false;
  @Input() isAssignmentRow?: boolean = false;
  isMobile: boolean = false;
  isMobileDevice: boolean = false;

  rowContentHoverValues: RowContentHoverValues;
  contentParams: IRouteParams;

  constructor(
    private scormService: ScormService,
    layoutService: LayoutService,
    dataService: DataService,
    authService: AuthService,
    instanceService: InstanceService,
    modalController: ModalController,
    private platform: Platform
  ) {
    super(dataService, authService, instanceService, modalController, layoutService);
  }

  ngOnInit() {
    this.content.featureDescriptors = this.removeParagraphTags(this.content.featureDescriptors!);
    this.detectMobileDevice();
    this.isMobile = this.layoutService.currentScreenSize === 'xs';
    this.setAssetUrls();
    this.setCompletionAchievementUrls();
    this.contentParams = { ...this.routeParams, instanceSlug: this.content.slug ?? this.content.id } as IRouteParams;
  }

  setAssetUrls() {
    if (this.content.externalLogoUrl) {
      this.iconUrl = this.content.externalLogoUrl;
    } else if (this.content.iconAssetId) {
      this.iconUrl = `${environment.contentUrl}asset/${this.content.iconAssetId}/content?height=300`;
    } else {
      this.iconUrl = 'assets/images/no-image.png'; // `${environment.contentUrl}asset/default/${this.instanceId}?systemProperty=${'Instance.IconAssetId'}`;
    }
  }

  private detectMobileDevice() {
    this.isMobileDevice = this.platform.is('mobile') || this.platform.is('android') || this.platform.is('ios');
  }

  //Function added to remove "<p>" tag from parsed content to allow content.featureDescriptors and content.Property2 to appear inline"
  removeParagraphTags(input: string): string {
    return input ? input.replace(/<\/?p>/g, '') : input;
  }

  updateRowContentHoverValues(value: any) {
    this.rowContentHoverValues = value;
  }

  generateScormFile(event: any) {
    this.dataService
      .getInstance(this.content.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(instance => {
        if (instance) {
          this.scormService.generateScormFile(instance, event);
        }
      });
  }
}
