<div class="parent-container">
  <ion-row>
    <ion-col class="back-col" size="12">
      <ion-button fill="clear" size="small" color="warning" (click)="back()"> <ion-icon slot="start" name="chevron-back-outline"></ion-icon>Back</ion-button>
      <ion-button fill="solid" size="small" color="warning" (click)="save()">Add</ion-button>
    </ion-col>
  </ion-row>
  <ng-container>
    <div class="inner-search-container">
      <div class="top-heading-container">
        <ion-label class="heading" position="stacked"> Components </ion-label>
        <br />
        <ion-label class="grey-text">Select components to add</ion-label>
        <div class="search-container">
          <ion-input placeholder="Find a Component" [(ngModel)]="query">
            <ion-icon class="search-icon" name="search-outline"></ion-icon>
          </ion-input>
        </div>
      </div>
      @if (dynamicComponentList && section.typeBw === sectionTypes.Dynamic) {
        <div class="dynamic-list-container">
          @for (component of getDynamicComponentListFiltered(); track component) {
            <div>
              <div class="component-container">
                <div class="image-heading-container">
                  <div class="image-heading">
                    {{ component.componentType?.name }}{{ component.templateField?.label !== component.componentType?.name ? ' -> ' + component.templateField?.label : '' }}
                  </div>
                </div>
                <div
                  [ngClass]="isSelected(component.id) ? 'component selected' : 'component'"
                  [ngStyle]="{ 'background-color': !component.componentType.assetId ? '#111111' : null }"
                  (click)="checkboxChanged(component.id)">
                  @if (component.componentType.assetId) {
                    <div class="image-container" [style]="'background-image:url(' + setImage(component.componentType.assetId) + ')'"></div>
                  }
                  @if (!component.componentType.assetId) {
                    <div class="component-left">
                      <div class="inner-container">
                        <div class="heading">
                          {{ component.componentType?.name }}{{ component.templateField?.label !== component.componentType?.name ? ' -> ' + component.templateField?.label : '' }}
                        </div>
                      </div>
                    </div>
                  }
                </div>
              </div>
            </div>
          }
        </div>
      }
    </div>
  </ng-container>
</div>
