import {Component, EventEmitter, Input, OnChang<PERSON>, OnDestroy, OnInit, Output, SimpleChanges} from '@angular/core';
import {IComponent, IDropDownLinkType} from "@app/core/contracts/contract";
import {Subject, Subscription, takeUntil} from "rxjs";
import {UntypedFormBuilder, UntypedFormGroup, Validators} from "@angular/forms";
import {KeyValue} from "@app/core/dtos/KeyValue";
import {qlikReportingTypes} from "@app/core/constants/data";
import {DataService} from "@app/core/services/data-service";

@Component({
    selector: 'app-qlik-analytics-type-editor',
    templateUrl: './qlik-analytics-type-editor.component.html',
    styleUrl: './qlik-analytics-type-editor.component.scss',
    standalone: false
})

export class QlikAnalyticsTypeEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  componentDestroyed$: Subject<boolean> = new Subject();
  qlikAnalyticsTypeFieldForm: UntypedFormGroup;
  formValueChanges$: Subscription;
  qlikReportingTypes: KeyValue[] = qlikReportingTypes;

  constructor(private formBuilder: UntypedFormBuilder, private dataService: DataService) {  }

  ngOnInit(): void {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.qlikAnalyticsTypeFieldForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      description1: [this.component?.templateField?.description1],
      label4: [this.component?.templateField?.label4],
      rowNumber: [this.component?.builderRowNumber],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.qlikAnalyticsTypeFieldForm) {
      return;
    }

    this.qlikAnalyticsTypeFieldForm.controls.label.setValue(this.component.templateField.label);
    this.qlikAnalyticsTypeFieldForm.controls.description1.setValue(this.component.templateField.description1);
    this.qlikAnalyticsTypeFieldForm.controls.label4.setValue(this.component.templateField.label4);
    this.qlikAnalyticsTypeFieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.qlikAnalyticsTypeFieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.qlikAnalyticsTypeFieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.qlikAnalyticsTypeFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.qlikAnalyticsTypeFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.qlikAnalyticsTypeFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.qlikAnalyticsTypeFieldForm.valid);
      this.setValues();
    });
  }


  setValues() {
    if (this.qlikAnalyticsTypeFieldForm.valid) {
      this.component.templateField.label = this.qlikAnalyticsTypeFieldForm.controls.label.value;
      this.component.templateField.description1 = this.qlikAnalyticsTypeFieldForm.controls.description1.value;
      this.component.templateField.label4 = this.getDropDownLinkValue().id ?? '';
      this.component.builderRowNumber = this.qlikAnalyticsTypeFieldForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.qlikAnalyticsTypeFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.qlikAnalyticsTypeFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.colspan = this.qlikAnalyticsTypeFieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.qlikAnalyticsTypeFieldForm.controls.colNumber.value;
    }
  }

  getDropDownLinkValue() {
    const link = this.qlikReportingTypes.find(x => x.id === this.qlikAnalyticsTypeFieldForm?.controls?.label4?.value);

    return { id: link?.id, title: link?.value } as IDropDownLinkType;
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
