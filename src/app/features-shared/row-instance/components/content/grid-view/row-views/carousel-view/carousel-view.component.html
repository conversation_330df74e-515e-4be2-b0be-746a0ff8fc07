<div class="parent-container">
  <swiper-container appSwiper #swiper [config]="swiperConfig" class="swiper">
    @for (item of content; track item) {
    <swiper-slide>
      <div
        [ngTemplateOutlet]="thumbnailTypesTemplate"
        [ngTemplateOutletContext]="{
            $implicit: item,
          }"></div>
    </swiper-slide>
    }
  </swiper-container>
  @if (!hidePrevNavButton) {
  <div (click)="slidePrev()" class="custom-prev">
    <mat-icon class="icon left-arrow" svgIcon="carousel-arrow"></mat-icon>
  </div>
  }
  @if (!hideNextNavButton) {
  <div (click)="slideNext()" class="custom-next">
    <mat-icon class="icon" svgIcon="carousel-arrow"></mat-icon>
  </div>
  }
</div>
