import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RowContentHoverValues } from '@app/shared/directives/row-content-hover.directive';
import { environment } from '@env/environment';
import { ModalController } from '@ionic/angular';
import { ThumbnailStyleBaseComponent } from '../../base/thumbnail-style-base.component';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-row-image-background-view',
    templateUrl: './image-background-view.component.html',
    styleUrls: ['./image-background-view.component.scss'],
    standalone: false
})
export class ImageBackgroundViewComponent extends ThumbnailStyleBaseComponent implements OnInit, On<PERSON><PERSON>roy {
  @Input() thumbnailType: string;
  @Input() isEducator: boolean = false;
  @Input() hasAdminAccess: boolean = false;
  @Input() hideAddButton: boolean = false;
  @Input() featureName: string;
  @Input() instanceName: string;
  @Input() status: string;
  @Input() featureDescription: string;
  @Input() property1: string;
  @Input() property2: string;
  @Input() coverAssetId: string;
  @Input() externalCoverUrl: string;
  @Input() iconAssetId: string;
  @Input() externalLogoUrl?: string;
  @Input() instanceId: string;
  @Input() entityType: number;
  @Input() actionUrl: string;
  @Input() isAssignmentRow?: boolean = false;
  rowContentHoverValues: RowContentHoverValues;

  constructor(dataService: DataService, authService: AuthService, instanceService: InstanceService, modalController: ModalController, layoutService: LayoutService) {
    super(dataService, authService, instanceService, modalController, layoutService);
  }

  ngOnInit() {
    this.setAssetUrls();
  }

  setAssetUrls() {
    if (this.coverAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.coverAssetId}/content?height=500`;
    } else {
      this.imageUrl = 'assets/images/no-image.png'; // `${environment.contentUrl}asset/default/${this.instanceId}?systemProperty=${'Instance.ImageAssetId'}`;
    }
    if (this.iconAssetId) {
      this.iconUrl = `${environment.contentUrl}asset/${this.iconAssetId}/content?height=500`;
    } else {
      this.iconUrl = 'assets/images/no-image.png'; // `${environment.contentUrl}asset/default/${this.instanceId}?systemProperty=${'Instance.IconAssetId'}`;
    }
  }
}
