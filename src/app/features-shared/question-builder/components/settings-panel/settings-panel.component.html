<ion-card style="--background-color:{{ backgroundColor }};">
  <ion-card-content>
    <ion-label>Settings</ion-label>
    <br />
    @if (question.questionType.name === 'Multiple Choice' || question.questionType.name === 'Dropdown') {
      <app-multiple-choice-settings [question]="question" (questionChanged)="questionUpdated()"></app-multiple-choice-settings>
    }
    @if (question.questionType.name === 'Matching List') {
      <app-matching-list-settings [question]="question" (questionChanged)="questionUpdated()"></app-matching-list-settings>
    }
    @if (question.questionType.name === 'Picture Matching') {
      <app-picture-matching-settings [question]="question" (questionChanged)="questionUpdated()"></app-picture-matching-settings>
    }
    @if (question.questionType.name === 'Text to Picture') {
      <app-text-to-picture-matching-settings [question]="question" (questionChanged)="questionUpdated()"></app-text-to-picture-matching-settings>
    }
    @if (question.questionType.name === 'Opinion Scale' || question.questionType.name === 'Interest Profiler') {
      <app-opinion-scale-settings [question]="question" (questionChanged)="questionUpdated()"></app-opinion-scale-settings>
    }
    <div class="button-row">
      <ion-button color="dark" fill="solid" (click)="fileExplorerOpenRef.click()">Edit Background</ion-button>
    </div>
    <input style="visibility: hidden" type="file" accept="image/*" #fileExplorerOpenRef id="fileDropRef" (change)="fileBrowseHandler($event)" />
  </ion-card-content>
</ion-card>
