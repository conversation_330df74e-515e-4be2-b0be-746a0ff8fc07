import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { IQuestion } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { map, Observable } from 'rxjs';

@Component({
    selector: 'app-opinion-scale-settings',
    templateUrl: './opinion-scale-settings.component.html',
    styleUrls: ['./opinion-scale-settings.component.scss'],
    standalone: false
})
export class OpinionScaleSettingsComponent implements OnInit {
  @Input() question: IQuestion;
  @Input() backgroundColor = '#181818';
  @Output() questionChanged = new EventEmitter<void>();
  formGroup: UntypedFormGroup;
  maxLength = 24;
  options$: Observable<KeyValue[]>;
  purposes: KeyValue[] = [
    { id: 'Interest', value: 'measures Interest' } as KeyValue,
    { id: 'Knowledge', value: 'measures Knowledge' } as KeyValue,
    { id: 'Identity', value: 'measures Identity' } as KeyValue,
  ];
  maxlength = 24;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.initData();
    this.setupForm();
  }

  initData() {
    this.options$ = this.dataService.getTagChildren('75AB828E-CC28-42AA-8D06-1FF2644E67FC').pipe(
      map(tags => {
        return tags.map(t => {
          return { id: t.id, value: t.name, hasChildren: t.inverseParent ?? false } as KeyValue;
        });
      })
    );
  }

  setupForm() {
    this.formGroup = this.formBuilder.group({
      scaleStart: [this.question.scaleStart],
      scaleEnd: [this.question.scaleEnd],
      scaleLabelOne: [this.question.scaleLabelOne],
      scaleLabelTwo: [this.question.scaleLabelTwo],
      scaleLabelThree: [this.question.scaleLabelThree],
      purpose: [this.question.purpose],
      occupation: [this.question.measuredTagId],
    });

    this.subscribeToForm();
  }

  subscribeToForm() {
    this.formGroup.valueChanges.subscribe(() => {
      this.question.scaleStart = this.formGroup.controls.scaleStart.value;
      this.question.scaleEnd = this.formGroup.controls.scaleEnd.value;
      this.question.scaleLabelOne = this.formGroup.controls.scaleLabelOne.value;
      this.question.scaleLabelTwo = this.formGroup.controls.scaleLabelTwo.value;
      this.question.scaleLabelThree = this.formGroup.controls.scaleLabelThree.value;
      this.question.purpose = this.formGroup.controls.purpose.value;
      this.question.measuredTagId = this.formGroup.controls.occupation.value;

      this.questionChanged.emit();
    });
  }
}
