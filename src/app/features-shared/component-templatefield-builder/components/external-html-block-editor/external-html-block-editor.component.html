@if (externalHtmlForm) {
  <form [formGroup]="externalHtmlForm">
    <ion-grid>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'External Url'" [placeHolder]="'Add External Url...'" formControlName="defaultImageUrl"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Row Number'" [placeHolder]="'Add field row number...'" formControlName="rowNumber" [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Column Number (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add column number (Out of 12)...'"
            formControlName="colNumber"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Colspan (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add field colspan (Out of 12)...'"
            formControlName="colspan"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Hover Sort Order'"
            [placeHolder]="'Add field hover sort order...'"
            formControlName="hoverSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [backgroundColor]="'#292929'"
            [label]="'Instance Sort Order'"
            [placeHolder]="'Add field instance sort order...'"
            formControlName="instanceSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-card>
            <ion-card-content>
              <app-field-checkboxes-base [baseForm]="externalHtmlForm"></app-field-checkboxes-base>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVisibleRepository">Is Visible in Repository</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="openExternal">Open in new tab</mat-slide-toggle>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
