.add-button-container {
  position: relative;
  display: flex;
  justify-content: flex-end;
}

.status-inprogress {
  margin-bottom: 0px;
}

.portrait {
  aspect-ratio: 4 / 5;
  border-radius: 7px;
  width: 100%;
}

.landscape {
  aspect-ratio: 3 / 2;
  height: 100%;
  border-radius: 5px;
}

.content {
  background-color: #111;
  background-image: var(--background-image);
  background-size: cover;
  min-height: 0;
  outline: 1px #333 solid;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'roboto';
  margin: 0;
  color: #aaa;
  width: 100%;
  letter-spacing: 0.01em;
  line-height: 1.1;
}

h5 {
  display: none;
}

h6 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-top: 3px;
  font-weight: 400;
  letter-spacing: 0.03em !important;
}

.text-container {
  position: absolute;
  bottom: 11px;
}

@media (min-width: 1501px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;

    h1 {
      bottom: 17px;
      color: #fff;
      font-size: 1.25em;
      font-weight: 900;
    }

    h5 {
      font-style: italic;
      font-size: 0.875em;
      font-weight: 900;
    }

    h6 {
      bottom: 0px;
      font-style: italic;
      font-size: 0.875em;
    }
  }
}

@media (min-width: 1281px) and (max-width: 1500px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;

    h1 {
      bottom: 17px;
      color: #fff;
      font-size: 1.125em;
      font-weight: 900;
    }

    h5 {
      font-style: italic;
      font-size: 0.875em;
      font-weight: 900;
    }

    h6 {
      bottom: 0px;
      text-overflow: ellipsis;
      font-style: italic;
      font-size: 0.875em;
    }
  }
}

@media (min-width: 993px) and (max-width: 1280px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;

    h1 {
      bottom: 17px;
      color: #fff;
      font-size: 1.063em;
      font-weight: 900;
    }

    h5 {
      font-style: italic;
      font-size: 0.875em;
      font-weight: 900;
    }

    h6 {
      bottom: 0px;
      text-overflow: ellipsis;
      font-style: italic;
      font-size: 0.875em;
    }
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;

    h1 {
      bottom: 17px;
      color: #fff;
      font-size: 1.063em;
      font-weight: 900;
    }

    h5 {
      font-style: italic;
      font-size: 0.875em;
      font-weight: 900;
    }

    h6 {
      bottom: 0px;
      text-overflow: ellipsis;
      font-style: italic;
      font-size: 0.875em;
    }
  }
}

@media (min-width: 480px) and (max-width: 768px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;
    padding-bottom: 7px;

    h1 {
      bottom: 16px;
      color: #fff;
      font-size: 1em;
      font-weight: 900;
    }

    h5 {
      font-style: italic;
      font-size: 0.75em;
      font-weight: 900;
      padding-bottom: 1px;
    }

    h6 {
      bottom: 0px;
      text-overflow: ellipsis;
      font-style: italic;
      font-size: 0.75em;
    }
  }
}

@media (max-width: 479px) {
  .text-container {
    bottom: 0px;
    width: 100%;
    margin-bottom: 4%;
    padding-left: 7%;
    padding-right: 5%;
    font-family: 'roboto';
    line-height: 1.1;
    letter-spacing: 0.01em;
    padding-bottom: 7px;

    h1 {
      bottom: 16px;
      color: #fff;
      font-size: .75em;
      font-weight: 900;
    }

    h5 {
      font-style: italic;
      font-size: 0.75em;
      font-weight: 900;
      padding-bottom: 1px;
    }

    h6 {
      bottom: 0px;
      text-overflow: ellipsis;
      font-style: italic;
      font-size: 0.6em;
    }
  }
}

h2 {
  font-size: 2.5em;
}

h3 {
  font-size: 2em;
}

h4 {
  font-size: 1.8em;
}

app-join-code {
  bottom: 54px;
  position: absolute;
  padding-bottom: 10px;
  top: 10px;
  height: 20px;
  max-width: 80px;
}

p {
  font-family: 'Roboto';
}

.gradient {
  // background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4) 70%, rgba(0, 0, 0, 0.7) 80%, rgba(0, 0, 0, 0.9) 90%);
  height: 100%;
  width: 100%;
  border-radius: 3px;
  position: relative;
  outline: 1px solid #333333;

  .badge-icon {
    position: absolute;
    left: 0px;
    top: -12px;
    z-index: 899;
    width: 90px;
    height: 90px;
    border-radius: 5px;
  }

  @media screen and (max-width: 479px) {
    .badge-icon {
      width: 50px;
      height: 50px;
    }
  }
}

.feature-gradient {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4) 70%, rgba(0, 0, 0, 0.7) 50%, rgba(0, 0, 0, 0.9) 50%);
}

.hover-item-org {
  background:
    linear-gradient(to bottom, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 80%, rgba(0, 0, 0, 0.9) 100%),
    var(--background-image) no-repeat,
    #fff !important;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.hover-item-default {
  background:
    linear-gradient(to bottom, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 80%, rgba(0, 0, 0, 0.9) 100%),
    var(--background-image) no-repeat;
  background-size: cover;
  background-color: #111;
}

.hover-item {
  background:
    linear-gradient(to bottom, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.5) 80px, rgba(0, 0, 0, 11) 140px),
    var(--background-image) no-repeat;
  border-radius: 7px;
  border: 1.5px solid #333;
  z-index: 900;
  position: absolute !important;
  top: 2px;
  margin-bottom: 6px;

  .icon-container {
    z-index: 999;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Roboto';
    margin: 0;
    color: #aaa;
  }

  h1 {
    color: #fff;
    font-size: 1.250em;
    font-weight: 900;
    line-height: 1.1;
    letter-spacing: 0.02em;
    padding-bottom: 2px;
    height: auto;
  }

  h2 {
    font-size: 2.5em;
  }

  h3 {
    font-size: 2em;
  }

  h4 {
    font-size: 1.2em;
    color: #aaa;
  }

  h5 {
    bottom: 40px;
    font-style: italic;
    font-size: 12px;
  }

  h6 {
    font-style: italic;
    font-size: 11px;
  }

  p {
    font-family: 'Roboto';
  }

  .actions-heading {
    position: relative;
    z-index: 101;
  }

  .status-foot-container {
    left: 10px;
    bottom: -7px;
    height: fit-content;
    width: max-content;
    max-width: 100%;
  }

  .instance-tracker-container {
    width: 100%;

    app-instance-tracking {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
  }

  .descriptions-heading {
    padding: 7px 15px 17px 15px;
    padding-left: 7%;
    padding-right: 5%;

    .gradient {
      left: 0;
      right: 0;
      top: 0;
      position: absolute;
      height: 100%;
      width: 100%;
    }

    .descriptors-container {
      top: 80px;
      padding-top: 85px;

      .inner-html {
        font-family: 'Roboto';
        color: #aaa;
        font-size: 0.925em;
        font-weight: 400;
        font-style: italic;
        line-height: 1.1;
        letter-spacing: 0.02em;
        height: auto;
        overflow: auto;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        padding-bottom: 5px;
      }

      .sub-description {
        font-family: 'Roboto';
        color: #aaa;
        font-size: 1.125em;
        overflow: hidden;
        -webkit-line-clamp: 4;
        font-weight: 400;
        line-height: 1.3;
        letter-spacing: 0.02em;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        margin-bottom: 5px;
      }
    }
  }

  ::ng-deep .inner-html {
    p {
      padding-bottom: 0px !important;
    }
  }

  ::ng-deep .parent-container .text-area-container {
    font-family: 'Roboto';
    font-size: 1em;
    color: #aaa;
    line-height: 1.3;
    font-weight: 400;
    letter-spacing: 0.01em;
    display: -webkit-box;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin: 4px 0px 4px 0px;
  }

  ::ng-deep .ql-editor {
    margin: 0px;
    padding: 0px;
    margin-top: 5px;
  }

  ::ng-deep ion-card {
    padding-top: 0;
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }
}

.hover-item-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.img-col {
  min-width: 60px;

  .row-icon {
    height: 40px;
    width: 40px;
    border-radius: 5px;
    margin: 0px 10px;
  }
}

:host ::ng-deep div.parent-container {
  margin-top: 0px;
  margin-right: 10px;
  border: 0px;
  margin-bottom: 0px !important;
}

:host ::ng-deep .parent-container {
  margin-top: 0px;
  margin-right: 10px;
  border: 0px;
  margin-bottom: 0px;
}

.background-layout {
  background-size: cover !important;
  position: relative;
}

.landscape-gradient {
  background:
    linear-gradient(to bottom, rgba(0, 0, 0, 0) 48%, rgba(0, 0, 0, 0.1) 52%, rgba(0, 0, 0, 0.65) 66%, rgba(0, 0, 0, 0.8) 73%, rgba(0, 0, 0, 0.9) 83%),
    var(--background-image) no-repeat;
}

.bigC-gradient {
  background:
    linear-gradient(to bottom, rgba(0, 0, 0, 0) 57%, rgba(0, 0, 0, 0.1) 61%, rgba(0, 0, 0, 0.65) 75%, rgba(0, 0, 0, 0.8) 81%, rgba(0, 0, 0, 0.9) 89%),
    var(--background-image) no-repeat;
}

.portrait-gradient {
  background:
    linear-gradient(to bottom, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.1) 60%, rgba(0, 0, 0, 0.65) 75%, rgba(0, 0, 0, 0.8) 81%, rgba(0, 0, 0, 0.9) 89%),
    var(--background-image) no-repeat;
  background-position: center;
}

.org-background {
  background:
    linear-gradient(to bottom, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 0.1) 60%, rgba(0, 0, 0, 0.65) 75%, rgba(0, 0, 0, 0.8) 81%, rgba(0, 0, 0, 0.9) 89%),
    var(--background-image) no-repeat,
    #fff;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  background-origin: content-box;
}

.cdk-drag-handle {
  color: #000000;
  z-index: 2;
  cursor: move;
}

.round-button {
  --padding-start: 0.75em !important;
  --padding-end: 0.75em !important;
}

.bottom-row {
  min-height: 150px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

ion-button {
  --background: #fff;
}

.right-side {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  align-content: center;
}

.inline-descriptors {
  display: flex !important;
  margin: 0;

  h6 {
    width: fit-content;
    padding-right: 5px;
    color: #cccccc;
  }
}
