﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AskForPersonalDataLog" xml:space="preserve">
    <value>کاربر با شناسه "{0}" اطلاعات شخصی شان را درخواست کرده است.</value>
  </data>
  <data name="AuthenticatorVerified" xml:space="preserve">
    <value>برنامه تایید هویت شما تایید شده است.</value>
  </data>
   <data name="ConfirmEmailBody" xml:space="preserve">
    <value>لطفا حساب خود را تایید کنید با &lt;a href='{0}'&gt;کلیک کردن اینجا&lt;/a&gt;.</value>
  </data>
  <data name="ConfirmEmailTitle" xml:space="preserve">
    <value>ایمیل خود را تایید کنید</value>
  </data>
  <data name="DeletePersonalData" xml:space="preserve">
    <value>کاربر با شناسه '{0}' حذف شان کرده است.</value>
  </data>
  <data name="Error2FANotEnabled" xml:space="preserve">
    <value>نمی توان کدهای بازیابی را برای کاربر با شناسه {0} ایجاد کرد بدلیل اینکه 2FA را فعال نکرده اند.</value>
  </data>
  <data name="ErrorCode" xml:space="preserve">
    <value>کد</value>
  </data>
  <data name="ErrorDeletingUser" xml:space="preserve">
    <value>خطای غیرمنتظره در حذف کاربر با شناسه {0} رخ داده است.</value>
  </data>
  <data name="ErrorDisable2FA" xml:space="preserve">
    <value>خطای غیرمنتظره در غیرفعال کردن 2FA برای کاربر با شناسه {0} رخ داده است.</value>
  </data>
  <data name="ErrorGenerateCodesWithout2FA" xml:space="preserve">
    <value>نمی توان کدهای بازیابی را برای کاربر ایجاد کرد بدلیل اینکه 2FA را فعال نکرده اند.</value>
  </data>
  <data name="ErrorLoadingExternalLogin" xml:space="preserve">
    <value>در بارگذاری اطلاعات ورود خارجی برای کاربر با شناسه {0} خطای غیرمنتظره رخ داده است.</value>
  </data>
  <data name="ErrorRemovingExternalLogin" xml:space="preserve">
    <value>در حذف ورود خارجی برای کاربر با شناسه {0} خطای غیرمنتظره رخ داده است.</value>
  </data>
  <data name="ErrorSettingEmail" xml:space="preserve">
    <value>خطای غیرمنتظره در تنظیم ایمیل برای کاربر با شناسه {0} رخ داده است.</value>
  </data>
  <data name="ErrorSettingPhone" xml:space="preserve">
    <value>خطای غیرمنتظره در تنظیم شماره تلفن برای کاربر با شناسه {0} رخ داده است.</value>
  </data>
  <data name="ExternalLoginAdded" xml:space="preserve">
    <value>ورود خارجی اضافه شد.</value>
  </data>
  <data name="ExternalLoginRemoved" xml:space="preserve">
    <value>ورود خارجی حذف شد.</value>
  </data>
  <data name="InvalidVerificationCode" xml:space="preserve">
    <value>کد تایید نامعتبر است.</value>
  </data>
  <data name="PasswordChanged" xml:space="preserve">
    <value>کلمه عبور شما تغییر کرده است.</value>
  </data>
  <data name="PasswordChangedLog" xml:space="preserve">
    <value>کاربر {0} کلمه عبور خود را با موفقیت تغییر داد.</value>
  </data>
  <data name="PasswordNotCorrect" xml:space="preserve">
    <value>کلمه عبور صحیح نیست.</value>
  </data>
  <data name="PasswordSet" xml:space="preserve">
    <value>کلمه عبور شما تنظیم شده است.</value>
  </data>
  <data name="ProfileUpdated" xml:space="preserve">
    <value>مشخصات شما به روز شده است</value>
  </data>
  <data name="SuccessDisabled2FA" xml:space="preserve">
    <value>کاربر با شناسه {0} 2fa را غیر فعال کرده است.</value>
  </data>
  <data name="SuccessForgetBrowser2FA" xml:space="preserve">
    <value>مرورگر فعلی فراموش شده است. هنگامی که دوباره از این مرورگر وارد سیستم شوید، از شما کد 2fa خواسته خواهد شد.</value>
  </data>
  <data name="SuccessResetAuthenticationKey" xml:space="preserve">
    <value>کاربر با شناسه {0} کلید برنامه تایید اعتبار خود را تنظیم مجدد کرده است.</value>
  </data>
  <data name="SuccessUserEnabled2FA" xml:space="preserve">
    <value>کاربر با شناسه {0} 2FA را با یک برنامه تایید هویت فعال کرده است.</value>
  </data>
  <data name="UserGenerated2FACodes" xml:space="preserve">
    <value>کاربر با شناسه {0} کد جدید بازیابی 2FA را ایجاد کرده است.</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>بارگذاری کاربر با شناسه {0} امکان پذیر نیست.</value>
  </data>
  <data name="VerificationSent" xml:space="preserve">
    <value>ایمیل تایید ارسال شد. لطفا ایمیل خود را چک کنید.</value>
  </data>
</root>