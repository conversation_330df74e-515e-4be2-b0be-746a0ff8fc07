import { NgModule } from '@angular/core';
import { SharedModule } from '@app/shared/shared.module';
import { QuestionActiveModule } from '../question-active/question-active.module';
import { featureComponents, standaloneComponents } from './question-builder.declarations';

@NgModule({
  declarations: [...featureComponents],
  imports: [...standaloneComponents, SharedModule, QuestionActiveModule],
  exports: [...featureComponents],
})
export class QuestionBuilderModule {}
