import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-page-banner-field-editor',
    templateUrl: './page-banner-field-editor.component.html',
    styleUrls: ['./page-banner-field-editor.component.scss'],
    standalone: false
})
export class PageBannerEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  pageBannerFieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.pageBannerFieldForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  setTypeBwValues(typeBw: number | undefined) {
    if (typeBw !== undefined) {
      return this.builderService.acceptedImageAndVideoFormats.filter(x => Number(x.id) & (typeBw ?? 0)).map(x => x.id);
    }
    return null;
  }

  createForm() {
    this.pageBannerFieldForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      placeHolderText: [this.component?.templateField?.placeHolderText, Validators.required],
      helpTitle: [this.component?.templateField?.helpTitle],
      helpDescription: [this.component?.templateField?.helpDescription],
      defaultImageUrl: [this.component?.templateField?.defaultImageUrl],
      rowNumber: [this.component?.builderRowNumber ?? 0],
      hoverSortOrder: [this.component?.hoverSortOrder ?? 0],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isCoverImage: [this.component?.templateField?.isCoverImage ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      fileTypeBw: [this.setTypeBwValues(this.component?.templateField?.fileTypeBw), Validators.required],
      minFileSize: [this.component?.templateField?.minFileSize],
      maxFileSize: [this.component?.templateField?.maxFileSize],
      isInherit: [this.component?.templateField?.isInherit ?? false],
      systemProperty: [this.component?.templateField?.systemProperty?.id],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      heightPx: [this.component.templateField.heightPx],
      showGradient: [this.component.templateField.showGradient],
      customGradient: [this.component.templateField.customGradient],
      moveToBack: [this.component.templateField.moveToBack],
      aspectRatio: [this.component.templateField.aspectRatio],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
      backgroundPosition: [this.component.templateField.backgroundPosition],
      backgroundSize: [this.component.templateField.backgroundSize],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.pageBannerFieldForm) {
      return;
    }

    this.pageBannerFieldForm.controls.label.setValue(this.component.templateField.label);
    this.pageBannerFieldForm.controls.placeHolderText.setValue(this.component.templateField.placeHolderText);
    this.pageBannerFieldForm.controls.helpTitle.setValue(this.component.templateField.helpTitle);
    this.pageBannerFieldForm.controls.helpDescription.setValue(this.component.templateField.helpDescription);
    this.pageBannerFieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.pageBannerFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.pageBannerFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.pageBannerFieldForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.pageBannerFieldForm.controls.isCoverImage.setValue(this.component.templateField.isCoverImage);
    this.pageBannerFieldForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.pageBannerFieldForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.pageBannerFieldForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.pageBannerFieldForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.pageBannerFieldForm.controls.defaultImageUrl.setValue(this.component.templateField.defaultImageUrl);
    this.pageBannerFieldForm.controls.fileTypeBw.setValue(this.setTypeBwValues(this.component?.templateField?.fileTypeBw));
    this.pageBannerFieldForm.controls.minFileSize.setValue(this.component.templateField.minFileSize);
    this.pageBannerFieldForm.controls.maxFileSize.setValue(this.component.templateField.maxFileSize);
    this.pageBannerFieldForm.controls.isInherit.setValue(this.component.templateField.isInherit);
    this.pageBannerFieldForm.controls.systemProperty.setValue(this.component.templateField.systemProperty);
    this.pageBannerFieldForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.pageBannerFieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.pageBannerFieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.pageBannerFieldForm.controls.heightPx.setValue(this.component.templateField.heightPx);
    this.pageBannerFieldForm.controls.showGradient.setValue(this.component.templateField.showGradient);
    this.pageBannerFieldForm.controls.customGradient.setValue(this.component.templateField.customGradient);
    this.pageBannerFieldForm.controls.moveToBack.setValue(this.component.templateField.moveToBack);
    this.pageBannerFieldForm.controls.aspectRatio.setValue(this.component.templateField.aspectRatio);
    this.pageBannerFieldForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);
    this.pageBannerFieldForm.controls.backgroundPosition.setValue(this.component.templateField.backgroundPosition);
    this.pageBannerFieldForm.controls.backgroundSize.setValue(this.component.templateField.backgroundSize);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.pageBannerFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.pageBannerFieldForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.pageBannerFieldForm.valid) {
      this.component.templateField.label = this.pageBannerFieldForm.controls.label.value;
      this.component.templateField.placeHolderText = this.pageBannerFieldForm.controls.placeHolderText.value;
      this.component.templateField.helpTitle = this.pageBannerFieldForm.controls.helpTitle.value;
      this.component.templateField.helpDescription = this.pageBannerFieldForm.controls.helpDescription.value;
      this.component.builderRowNumber = this.pageBannerFieldForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.pageBannerFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.pageBannerFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.pageBannerFieldForm.controls.isRequiredField.value;
      this.component.templateField.isCoverImage = this.pageBannerFieldForm.controls.isCoverImage.value;
      this.component.templateField.isBuilderEnabled = this.pageBannerFieldForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.pageBannerFieldForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.pageBannerFieldForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.pageBannerFieldForm.controls.isViewField.value;
      this.component.templateField.defaultImageUrl = this.pageBannerFieldForm.controls.defaultImageUrl.value;
      this.component.templateField.fileTypeBw = this.pageBannerFieldForm.controls.fileTypeBw.value.reduce((a: number, b: number) => a + b, 0);
      this.component.templateField.minFileSize = this.pageBannerFieldForm.controls.minFileSize.value;
      this.component.templateField.maxFileSize = this.pageBannerFieldForm.controls.maxFileSize.value;
      this.component.templateField.isInherit = this.pageBannerFieldForm.controls.isInherit.value;
      this.component.templateField.isVisibleRepository = this.pageBannerFieldForm.controls.isVisibleRepository.value;
      this.component.templateField.colspan = this.pageBannerFieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.pageBannerFieldForm.controls.colNumber.value;
      this.component.templateField.heightPx = this.pageBannerFieldForm.controls.heightPx.value;
      this.component.templateField.showGradient = this.pageBannerFieldForm.controls.showGradient.value;
      this.component.templateField.customGradient = this.pageBannerFieldForm.controls.customGradient.value;
      this.component.templateField.moveToBack = this.pageBannerFieldForm.controls.moveToBack.value;
      this.component.templateField.aspectRatio = this.pageBannerFieldForm.controls.aspectRatio.value;
      this.component.templateField.useMaxWidth = this.pageBannerFieldForm.controls.useMaxWidth.value;
      this.component.templateField.backgroundPosition = this.pageBannerFieldForm.controls.backgroundPosition.value;
      this.component.templateField.backgroundSize = this.pageBannerFieldForm.controls.backgroundSize.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
