import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { IRouteParams } from '@app/core/contracts/contract';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RowContentHoverValues } from '@app/shared/directives/row-content-hover.directive';
import { environment } from '@env/environment';
import { ModalController } from '@ionic/angular';
import { ThumbnailStyleBaseComponent } from '../../base/thumbnail-style-base.component';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-row-photo-and-text',
    templateUrl: './photo-and-text.component.html',
    styleUrls: ['./photo-and-text.component.scss'],
    standalone: false
})
export class PhotoAndTextComponent extends ThumbnailStyleBaseComponent implements OnInit, OnDestroy {
  rowContentHoverValues: RowContentHoverValues;
  contentParams: IRouteParams;
  @Input() isEducator: boolean = false;
  @Input() hasAdminAccess: boolean = false;
  @Input() hideAddButton: boolean = false;
  @Input() isAssignmentRow?: boolean = false;
  constructor(dataService: DataService, authService: AuthService, instanceService: InstanceService, modalController: ModalController, layoutService: LayoutService) {
    super(dataService, authService, instanceService, modalController, layoutService);
  }

  ngOnInit() {
    if (this.content.coverAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.content.coverAssetId}/content?height=600`;
    } else {
      this.imageUrl = 'assets/images/no-image.png';
    }
    this.setAssetUrls();
    this.setCompletionAchievementUrls();
  }

  setAssetUrls() {
    if (this.content.iconAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.content.iconAssetId}/content?height=600`;
    } else if (this.content.coverAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.content.coverAssetId}/content?height=600`;
    } else {
      this.imageUrl = 'assets/images/no-image.png';
    }
  }

  updateRowContentHoverValues(value: any) {
    this.rowContentHoverValues = value;
  }
}
