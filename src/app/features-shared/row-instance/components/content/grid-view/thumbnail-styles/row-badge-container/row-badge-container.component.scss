.achievements-parent-container {
  .parent-container-notstarted {
    background-color: rgb(58, 58, 58);
    background-size: cover;
    width: 100%;
    border-radius: 0.5em;
    padding: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: left;

    .img-col {
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        padding: 10px;
        height: 80px;
        width: 80px;
      }
    }

    .heading-container {
      margin-bottom: 5px;
      .top-description {
        align-items: center;
        display: flex;
        .title {
          color: white;
          font-size: 20px;
          font-weight: bold;
        }
      }

      .sub-description {
        color: rgb(188, 188, 188);
        font-size: 16px;
        .inner-sub {
          color: white;
          font-weight: bold;
        }
      }
      .issued-by {
        color: rgb(188, 188, 188);
        font-size: 12px;
        font-style: italic;
        .inner-sub {
          color: white;
          font-weight: bold;
          text-decoration: underline;
        }
      }
    }

    .content-container {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      color: rgb(188, 188, 188);
    }
  }

  .parent-container-inprogress {
    background-color: rgb(58, 58, 58);
    background-size: cover;
    width: 100%;
    border-radius: 0.5em;
    padding: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: left;
    border: 2px solid orange;

    .img-col {
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        padding: 10px;
        height: 80px;
        width: 80px;
      }
    }

    .heading-container {
      margin-bottom: 5px;
      .top-description {
        align-items: center;
        display: flex;
        .title {
          color: white;
          font-size: 20px;
          font-weight: bold;
        }
      }

      .sub-description {
        color: rgb(188, 188, 188);
        font-size: 16px;
        .inner-sub {
          color: white;
          font-weight: bold;
        }
      }

      .issued-by {
        color: rgb(188, 188, 188);
        font-size: 12px;
        font-style: italic;

        .inner-sub {
          color: white;
          font-weight: bold;
          text-decoration: underline;
        }
      }
    }

    .content-container {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      color: rgb(188, 188, 188);
    }

    .finish-button-container {
      display: flex;
      justify-content: flex-end;
      ion-button {
        height: 30px;
        width: 60px;
        font-size: 14px;
        border-radius: 5px;
        background-color: white;
        color: black;
      }
    }
  }

  .in-progress-foot-container {
    position: relative;
    top: -22px;
    left: 50px;
    .inner {
      padding: 2px;
      font-size: 12px;
      border-radius: 5px;
      color: white;
      z-index: 999;
    }
  }

  .parent-container-completed {
    background-color: rgb(58, 58, 58);
    background-size: cover;
    width: 100%;
    border-radius: 0.5em;
    padding: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: left;
    border: 2px solid green;

    .img-col {
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        padding: 10px;
        height: 80px;
        width: 80px;
      }
    }

    .heading-container {
      margin-bottom: 5px;
      .top-description {
        align-items: center;
        display: flex;
        .title {
          color: white;
          font-size: 20px;
          font-weight: bold;
        }

        .icon-container {
          margin-left: 10px;
          ion-icon {
            font-size: 25px;
            color: green;
          }
        }
      }

      .sub-description {
        font-size: 16px;
        margin-bottom: 5px;

        .earned-container {
          padding: 3px;
          border-radius: 5px;
          background-color: green;
          color: white;
          margin-right: 5px;
        }

        .inner-sub {
          color: rgb(188, 188, 188);
        }
      }

      .issued-by {
        color: rgb(188, 188, 188);
        font-size: 12px;
        font-style: italic;

        .inner-sub {
          color: white;
          font-weight: bold;
          text-decoration: underline;
        }
      }
    }

    .content-container {
      color: rgb(188, 188, 188);
    }
  }

  .completed-foot-container {
    position: relative;
    top: -22px;
    left: 50px;
    .inner {
      background-color: green;
      padding: 2px;
      font-size: 12px;
      border-radius: 5px;
      color: white;
      z-index: 999;
    }
  }
}
