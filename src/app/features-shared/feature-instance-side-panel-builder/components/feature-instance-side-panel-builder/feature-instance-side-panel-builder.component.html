<div class="side-panel-builder-container">
  <mat-sidenav-container id="#side-nav-container" [ngClass]="onlyContent ? 'side-nav-container no-header' : 'side-nav-container with-header'">
    <mat-sidenav id="#side-nav" class="side-nav-inner-container" position="start" mode="side" [(opened)]="openMenu" [style.width.%]="drawerWidth">
      @if (onlyContent) {
        <div class="small-header">
          <div class="image-header-gradient" [style]="'--background-image:url(' + assetUrl + ');'">
            <ion-row class="inner-container">
              <ion-col class="back-button-col">
                <ion-fab-button size="small" class="back-btn" aria-label="Go Back" (click)="goBack(routeParams.viewType)">
                  <ion-icon name="arrow-back-outline"></ion-icon>
                </ion-fab-button>
              </ion-col>
              <ion-col class="header-col" size="10">
                <div class="description-container">
                  <span class="description">You are creating a</span>
                </div>
                <div class="title-container">
                  <span class="title">{{ instance?.feature?.title ?? '' | parsePipe: instance?.id | async }}. <ion-icon name="information-circle-outline"></ion-icon></span>
                </div>
              </ion-col>
            </ion-row>
          </div>
        </div>
      }
      <app-builder-side-panel
        [editPanelBuilderIn]="editPanelBuilder"
        [editInstanceSectionsIn]="editInstanceSections"
        [instance]="instance"
        [template]="template"
        [id]="getId() ?? ''"
        [isManager]="isManager"
        [onlyContent]="onlyContent"
        [routeParams]="routeParams"
        (menuClosed)="closeMenu()"
        (detectChanges)="forceChangeDetection()"
        (editPanelBuilderChanged)="editPanelBuilderChanged()">
      </app-builder-side-panel>
    </mat-sidenav>
    <mat-sidenav-content class="builder-content" [style.width.%]="contentWidth" [style.margin-left.%]="leftMargin !== 0 ? leftMargin : null">
      @if (openMenu === true) {
        <div class="cdk-drag-handle" cdkDrag cdkDragLockAxis="x" (cdkDragMoved)="onDragMove($event)" [cdkDragFreeDragPosition]="dragPosition">
          <ion-icon cdkDragHandle class="svg-icon" src="/assets/icons/ExpandIcon.svg"></ion-icon>
        </div>
      }
      <app-builder-view-options-row
        (publishClicked)="updateInstanceStatus($event)"
        [instance]="instance"
        [showPublishedOptions]="!isManager"
        [publishDisabled]="!publishEnabled()"></app-builder-view-options-row>
      <div class="preview-parent-content-container" [ngClass]="onlyContent === true ? 'preview-parent-container-height' : 'preview-parent-container-height-with-header'">
        <app-builder-instance-preview
          [id]="getId() ?? ''"
          [panelState]="openMenu"
          [template]="template"
          [instance]="instance"
          [routeParams]="routeParams"
          [disabled]="!publishEnabled()"
          (setupStepsOnEditClick)="setupSidePanelBuilder($event)"
          [editInstanceSections]="editInstanceSections"
          [editPanelBuilderIn]="editPanelBuilder"
          (editAddClicked)="sideBuilderUpdated()">
        </app-builder-instance-preview>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>
