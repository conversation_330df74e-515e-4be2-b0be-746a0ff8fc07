﻿using Microsoft.AspNetCore.Identity;
using System;
using System.Security.Cryptography;
using System.Text;

namespace EdgeFactor.ID.STS.Identity.Helpers
{
    public class CustomPasswordHasher<TUser> : PasswordHasher<TUser> where TUser : class
    {
        public CustomPasswordHasher()
        {
        }

        public override PasswordVerificationResult VerifyHashedPassword(TUser user, string hashedPassword, string providedPassword)
        {
            if (hashedPassword == null) { throw new ArgumentNullException(nameof(hashedPassword)); }
            if (providedPassword == null) { throw new ArgumentNullException(nameof(providedPassword)); }

            var bytes = new Span<byte>(new byte[256]);
            //EF old passwords not 64 string data check and skip if needed
            if (Convert.TryFromBase64String(hashedPassword, bytes, out var bytesWritten))
            {
                byte[] decodedHashedPassword = Convert.FromBase64String(hashedPassword);

                // read the format marker from the hashed password
                if (decodedHashedPassword.Length == 0)
                {
                    return PasswordVerificationResult.Failed;
                }

                // ASP.NET Core uses 0x00 and 0x01, so we start at the other end
                if (decodedHashedPassword[0] == 0xFF)
                {
                    if (VerifyHashedPasswordCustom(decodedHashedPassword, providedPassword))
                    {
                        return PasswordVerificationResult.SuccessRehashNeeded;
                    }
                    else
                    {
                        return PasswordVerificationResult.Failed;
                    }
                }
                else if (decodedHashedPassword[0] == 0x00 || decodedHashedPassword[0] == 0x01)
                {
                    return base.VerifyHashedPassword(user, hashedPassword, providedPassword);
                }
                else
                {
                    if (Verify(hashedPassword, providedPassword))
                    {
                        return PasswordVerificationResult.Success;
                    }
                    else
                    {
                        return PasswordVerificationResult.Failed;
                    }
                }
            }
            else
            {
                if (Verify(hashedPassword, providedPassword))
                {
                    return PasswordVerificationResult.SuccessRehashNeeded;
                }
                else
                {
                    return PasswordVerificationResult.Failed;
                }
            }
        }

        private static bool VerifyHashedPasswordCustom(byte[] hashedPassword, string password)
        {
            if (hashedPassword.Length < 2)
            {
                return false;
            }

            //convert back to string for Custom, ignoring first byte
            var storedHash = Encoding.UTF8.GetString(hashedPassword, 1, hashedPassword.Length - 1);
            return Verify(storedHash, password);
        }

        //EF Custom md5 password encryption 
        private static bool Verify(string hash, string password)
        {
            var pwHash = $"w{password.Trim()}p";
            var encoding = new System.Text.UnicodeEncoding();
            byte[] keyByte = encoding.GetBytes(pwHash);
            using MD5 md5 = MD5.Create();
            byte[] hashmessage = md5.ComputeHash(keyByte);
            var x = Convert.ToBase64String(hashmessage);
            x = x.Replace("/", "-");
            x = x.Replace("'", "-");
            x = x.Replace("\"", "-");
            x = x.Replace(".", "-");
            x = x.Replace("=", "-");
            x = x.Replace("+", "-");
            x = x.Replace("~", "-");
            return x == hash;
        }
    }
}
