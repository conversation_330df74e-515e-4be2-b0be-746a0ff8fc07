<div appRowContentHover (rowContentHoverValuesChanged)="updateRowContentHoverValues($event)">
  <app-thumbnail-icons
    [row]="row"
    [content]="content"
    [instance]="instance"
    [readingMode]="readingMode"
    [isDraggable]="isDraggable"
    [isEducator]="isEducator"
    [hasAdminAccess]="hasAdminAccess"
    [hideAddButton]="hideAddButton"
    [canHover]="true"
    [onHover]="false"
    [isAssignmentRow]="isAssignmentRow"
    (contentRemoved)="emitContentRemoved()"
    (editCustomRowContent)="editContent()">
  </app-thumbnail-icons>
  @if (!rowContentHoverValues?.showHover) {
    <div
      class="parent-layout"
      style="--background-image: url('{{ imageUrl }}')"
      [ngClass]="{
        'status-inprogress': content.status === 'InProgress',
        'status-complete': content.status === 'Completed',
      }">
      <div class="content-area" [ngClass]="alignment === 'center' ? 'class-center' : ''">
        <div class="parent-heading-container">
          <div class="title">
            {{ content.instanceName }}
          </div>
          <div class="description">{{ content.instanceDescription }}</div>
        </div>
        @if (!content.action) {
          <div class="button-container">
            @if (content.actionUrl !== undefined) {
              <ion-button [title]="content.actionUrl" (click)="route()"> OPEN NOW </ion-button>
            }
          </div>
        } @else if (content.action) {
          <div class="button-container">
            <ion-button [title]="content.actionUrl" (click)="route()">
              {{ content.action }}
            </ion-button>
          </div>
        }
        @if (content.status) {
          <div class="status-foot-container" loading="lazy">
            @if (content.status === 'InProgress') {
              <span class="inner" style="background-color: orange">
                <span>IN PROGRESS</span>
              </span>
            }
            @if (content.status === 'Completed') {
              <span class="inner" style="background-color: green">
                <span>COMPLETED</span>
              </span>
            }
          </div>
        }
        @if (content?.achievementCompletion) {
          <div role="button" aria-disabled="true" class="badge-icon" (click)="openAchievementCompletionModalOnClick($event)" (keydown)="openAchievementCompletionModalOnClick($event)">
            <img ngSrc="{{ badgeImageIcon }}" fill="true" alt />
          </div>
        }
      </div>
    </div>
  }

  @if (rowContentHoverValues?.showHover) {
    <div
      appRowContentHoverForeground
      [hoverValues]="rowContentHoverValues"
      [ngClass]="{
        'status-inprogress': content.status === 'InProgress',
        'status-complete': content.status === 'Completed',
        'hover-item-org': content.entityType === entityTypes.Organizations,
        'hover-item-default': content.entityType !== entityTypes.Organizations,
      }"
      class="hover-item parent-layout background-layout"
      style="--background-image: url('{{ imageUrl }}')">
      <div class="actions-heading">
        <ion-row>
          <app-thumbnail-icons
            [row]="row"
            [content]="content"
            [instance]="instance"
            [readingMode]="readingMode"
            [isDraggable]="isDraggable"
            [isEducator]="isEducator"
            [canHover]="true"
            [onHover]="true"
            [isAssignmentRow]="isAssignmentRow"
            (contentRemoved)="emitContentRemoved()"
            (editCustomRowContent)="editContent()">
          </app-thumbnail-icons>
        </ion-row>
        <div class="tracking">
          <div class="tracking-heading">
            <app-instance-tracking [instanceId]="content.id" [status]="content.status"></app-instance-tracking>
          </div>
        </div>
        <div class="content-area" [ngClass]="alignment === 'center' ? 'class-center' : ''">
          <div class="parent-heading-container">
            <div class="title">
              {{ content.instanceName }}
            </div>
            <div class="description-hover">{{ content.instanceDescription }}</div>
          </div>
          @if (!content.action) {
            <div class="button-container">
              @if (content.actionUrl !== undefined) {
                <ion-button [title]="content.actionUrl" (click)="route()"> OPEN NOW </ion-button>
              }
            </div>
          } @else if (content.action) {
            <div class="button-container">
              <ion-button [title]="content.actionUrl" (click)="route()">
                {{ content.action }}
              </ion-button>
            </div>
          }
          @if (content.status) {
            <div class="status-foot-container" loading="lazy">
              @if (content.status === 'InProgress') {
                <span class="inner" style="background-color: orange">
                  <span>IN PROGRESS</span>
                </span>
              }
              @if (content.status === 'Completed') {
                <span class="inner" style="background-color: green">
                  <span>COMPLETED</span>
                </span>
              }
            </div>
          }
          @if (content?.achievementCompletion) {
            <div role="button" aria-disabled="true" class="badge-icon" (click)="openAchievementCompletionModalOnClick($event)" (keydown)="openAchievementCompletionModalOnClick($event)">
              <img ngSrc="{{ badgeImageIcon }}" fill="true" alt />
            </div>
          }
        </div>
      </div>
    </div>
  }
</div>
