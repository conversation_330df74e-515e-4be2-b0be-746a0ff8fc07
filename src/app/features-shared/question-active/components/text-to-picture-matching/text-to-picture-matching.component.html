<div class="parent-container" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }">
  <ion-card class="card-container">
    <ion-row class="top-description-row"> </ion-row>
    @if (matchingItem.itemPrompts && matchingItem.itemPrompts.length > 0) {
      @for (promptItem of matchingItem.itemPrompts; track promptItem; let i = $index) {
        <div class="prompt-item-row">
          <div class="inner-content">
            <div class="text-input-prompt-container">
              @if (i === 0) {
                <div class="prompt-header">PROMPTS</div>
              }
              <app-file-upload-control [defaultImageUrl]="promptItem.promptTitle" (valueUpdated)="saveInputTextDirect($event, 'promptTitle', promptItem)"></app-file-upload-control>
            </div>
            <div [ngClass]="i === 0 ? 'middle-arrow-row top-margin-down' : 'middle-arrow-row'"><ion-icon name="arrow-forward-outline"></ion-icon></div>
            <div class="text-input-answer-container">
              @if (i === 0) {
                <div class="answer-header">ANSWER</div>
              }
              <ion-input [value]="promptItem.answerTitle" (ionChange)="saveInputTextDirect($event, 'answerTitle', promptItem)" placeHolder="Type here..."></ion-input>
            </div>
            <ng-container>
              <div [ngClass]="i === 0 ? 'remove-container-right top-margin-down' : 'remove-container-right'">
                <ion-icon (click)="removePromptItem(promptItem)" class="trash-icon" name="trash"></ion-icon>
              </div>
            </ng-container>
          </div>
          <ng-container>
            <div class="div-line">
              <hr />
            </div>
          </ng-container>
        </div>
      }
    }
    <ng-container>
      <ion-row>
        <ion-col class="add-item-col">
          <ion-icon (click)="addPromptItem()" name="add-circle"></ion-icon>
          <div class="add-button-text">Add Pair</div>
        </ion-col>
      </ion-row>
    </ng-container>
  </ion-card>
</div>
