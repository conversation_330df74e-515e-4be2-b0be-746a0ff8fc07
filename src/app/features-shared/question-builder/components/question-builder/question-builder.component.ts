import { Component, EventEmitter, forwardRef, Input, OnInit, Output } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IAsset, IComponent, IQuestion, IQuestionType } from '@app/core/contracts/contract';
import { AlertService } from '@app/core/services/alert-service';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';
import { DataService } from '@app/core/services/data-service';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { environment } from '@env/environment';
import { debounceTime, Subject } from 'rxjs';

@Component({
    selector: 'app-question-builder',
    templateUrl: './question-builder.component.html',
    styleUrls: ['./question-builder.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => QuestionBuilderComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => QuestionBuilderComponent),
        },
    ],
    standalone: false
})
export class QuestionBuilderComponent extends BaseControlComponent implements OnInit {
  @Input() question: IQuestion;
  @Input() component: IComponent;
  @Input() backgroundColor = '#181818';
  @Input() itemBackgroundColor = 'none';
  @Input() onlyQuestion = false;
  @Output() questionUpdated = new EventEmitter<IQuestion>();
  questionChanged = new Subject<any>();
  questionTypes: IQuestionType[];
  selectedQuestionTypeId: string;
  imageUrl: string;
  asset: IAsset;
  questionUpdated$ = new Subject<any>();

  constructor(
    private dataService: DataService,
    private alertService: AlertService,
    private signalService: ComponentUpdateSignalService
  ) {
    super();
  }

  updateValue() {
    this.question = { ...this.question } as IQuestion;
    if(this.component.id)
    {
      this.signalService.triggerSignal({componentId : this.component.id, updateValue : JSON.stringify(this.question)})
    }
    this.questionChanged.next(null);
    this.questionUpdated$.next(null);
  }

  setBackgroundImage(assetId: any) {
    if (assetId && assetId !== '') {
      this.dataService.getAssetDetailsById(assetId).subscribe(asset => {
        if (asset) {
          this.asset = asset;
          this.imageUrl = `${environment.contentUrl}asset/${this.asset.id}/content`;
          this.question.backgroundImageAssetId = this.asset.id;
        }
      });
    }
    this.updateValue();
  }

  ngOnInit() {
    this.selectedQuestionTypeId = this.question.questionType.id;
    this.getQuestionTypes();
    this.questionUpdated$.pipe(debounceTime(700)).subscribe(() => {
      this.questionUpdated.next(this.question);
    });
  }

  setQuestionText(event: any) {
    this.question.questionText = event;
    this.updateValue();
  }

  setQuestionAnswerText(event: any) {
    this.question.answerText = event;
    this.updateValue();
  }

  questionUpdate() {
    this.updateValue();
  }

  getQuestionTypes() {
    this.dataService.getQuestionTypes().subscribe(data => {
      this.questionTypes = data;
    });
  }

  setQuestionType(event: any) {
    this.alertService.presentAlert('Are you sure you want to change the question type?', "You may lose any options you've added.").then(() => {
      this.question.questionType = this.questionTypes.find(x => x.id === event.target.value) as IQuestionType;
      this.selectedQuestionTypeId = event.target.value;
      this.updateValue();
    });
  }
}
