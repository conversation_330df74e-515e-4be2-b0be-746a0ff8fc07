.player-row-flex {
  display: flex;
  flex-direction: row;
  height: 100%;

  .player-width {
    width: 100%;
    position: relative;
      height: 100%;

    .player-inner-container {
      width: 100%;
      float: left;
      height: 100%;

      .top-border {
        border-color: #171717;
        border-width: 1px;
        border-style: solid;
        width: 100%;
      }

      .content-wrapper {
        height: 100%;
      }

      .player-view-info-container {
        width: 100%;
        height: 100%;
        margin-top: 5px;
      }

      .player-outer-container {
        margin-right: var(--page-margin-right-player);
        margin-left: var(--page-margin-left-player);
        overflow-y: auto;
        height: calc(100% - 50px);
        .player-container {
          border: 1px solid #f99e00;
          background-color: #393939;
          border-radius: 11px;
        }
      }
    }
  }

  .player-side-panel-container {
    overflow-y: auto;
    height: calc(100vh - 165px);
    width: 20%;
    background-color: #292929;
    margin-top: 5px;
    margin-right:5px;
    border-radius: 10px;
  }
}
