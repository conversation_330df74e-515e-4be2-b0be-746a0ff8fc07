﻿using AutoMapper;
using EdgeFactor.ID.Admin.Api.Configuration.Constants;
using EdgeFactor.ID.Admin.Api.Dtos.Roles;
using EdgeFactor.ID.Admin.Api.Dtos.Users;
using EdgeFactor.ID.Admin.Api.ExceptionHandling;
using EdgeFactor.ID.Admin.Api.Helpers.Localization;
using EdgeFactor.ID.Admin.Api.Resources;
using EdgeFactor.ID.Admin.EntityFramework.Shared.DbContexts;
using IdentityModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Skoruba.IdentityServer4.Admin.BusinessLogic.Identity.Dtos.Identity;
using Skoruba.IdentityServer4.Admin.BusinessLogic.Identity.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace EdgeFactor.ID.Admin.Api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [TypeFilter(typeof(ControllerExceptionFilterAttribute))]
    [Produces("application/json", "application/problem+json")]
    [Authorize(Policy = AuthorizationConsts.AdministrationPolicy)]

    public class UsersController<TUserDto, TRoleDto, TUser, TRole, TKey, TUserClaim, TUserRole, TUserLogin, TRoleClaim, TUserToken,
            TUsersDto, TRolesDto, TUserRolesDto, TUserClaimsDto,
            TUserProviderDto, TUserProvidersDto, TUserChangePasswordDto, TRoleClaimsDto, TUserClaimDto, TRoleClaimDto> : ControllerBase
        where TUserDto : UserDto<TKey>, new()
        where TRoleDto : RoleDto<TKey>, new()
        where TUser : IdentityUser<TKey>
        where TRole : IdentityRole<TKey>
        where TKey : IEquatable<TKey>
        where TUserClaim : IdentityUserClaim<TKey>
        where TUserRole : IdentityUserRole<TKey>
        where TUserLogin : IdentityUserLogin<TKey>
        where TRoleClaim : IdentityRoleClaim<TKey>
        where TUserToken : IdentityUserToken<TKey>
        where TUsersDto : UsersDto<TUserDto, TKey>
        where TRolesDto : RolesDto<TRoleDto, TKey>
        where TUserRolesDto : UserRolesDto<TRoleDto, TKey>
        where TUserClaimsDto : UserClaimsDto<TUserClaimDto, TKey>, new()
        where TUserProviderDto : UserProviderDto<TKey>
        where TUserProvidersDto : UserProvidersDto<TUserProviderDto, TKey>
        where TUserChangePasswordDto : UserChangePasswordDto<TKey>
        where TRoleClaimsDto : RoleClaimsDto<TRoleClaimDto, TKey>
        where TUserClaimDto : UserClaimDto<TKey>
        where TRoleClaimDto : RoleClaimDto<TKey>
    {
        private readonly IIdentityService<TUserDto, TRoleDto, TUser, TRole, TKey, TUserClaim, TUserRole, TUserLogin, TRoleClaim, TUserToken,
            TUsersDto, TRolesDto, TUserRolesDto, TUserClaimsDto,
            TUserProviderDto, TUserProvidersDto, TUserChangePasswordDto, TRoleClaimsDto, TUserClaimDto, TRoleClaimDto> _identityService;
        private readonly IGenericControllerLocalizer<UsersController<TUserDto, TRoleDto, TUser, TRole, TKey, TUserClaim, TUserRole, TUserLogin, TRoleClaim, TUserToken,
            TUsersDto, TRolesDto, TUserRolesDto, TUserClaimsDto,
            TUserProviderDto, TUserProvidersDto, TUserChangePasswordDto, TRoleClaimsDto, TUserClaimDto, TRoleClaimDto>> _localizer;
        private readonly UserManager<TUser> _userManager;
        private readonly IMapper _mapper;
        private readonly IApiErrorResources _errorResources;
        private readonly AdminIdentityDbContext _dbContext;
        private readonly IConfiguration _configuration;

        public UsersController(IIdentityService<TUserDto, TRoleDto, TUser, TRole, TKey, TUserClaim, TUserRole, TUserLogin, TRoleClaim, TUserToken,
                TUsersDto, TRolesDto, TUserRolesDto, TUserClaimsDto,
                TUserProviderDto, TUserProvidersDto, TUserChangePasswordDto, TRoleClaimsDto, TUserClaimDto, TRoleClaimDto> identityService,
            IGenericControllerLocalizer<UsersController<TUserDto, TRoleDto, TUser, TRole, TKey, TUserClaim, TUserRole, TUserLogin, TRoleClaim, TUserToken,
                TUsersDto, TRolesDto, TUserRolesDto, TUserClaimsDto,
                TUserProviderDto, TUserProvidersDto, TUserChangePasswordDto, TRoleClaimsDto, TUserClaimDto, TRoleClaimDto>> localizer, IMapper mapper, IApiErrorResources errorResources, AdminIdentityDbContext dbContext, UserManager<TUser> userManager, IConfiguration config)
        {
            _identityService = identityService;
            _localizer = localizer;
            _mapper = mapper;
            _errorResources = errorResources;
            _dbContext = dbContext;
            _userManager = userManager;
            _configuration = config;

        }

        [HttpGet("{id}")]
        public async Task<ActionResult<TUserDto>> Get(TKey id)
        {
            var user = await _identityService.GetUserAsync(id.ToString());
            return Ok(user);
        }

        [HttpGet]
        public async Task<ActionResult<TUsersDto>> Get(string searchText, int page = 1, int pageSize = 10)
        {
            var usersDto = await _identityService.GetUsersAsync(searchText, page, pageSize);

            return Ok(usersDto);
        }

        [HttpPost("Add")]
        public async Task<ActionResult<TUserDto>> Post([FromBody] TUserDto user)
        {
            if (!EqualityComparer<TKey>.Default.Equals(user.Id, default))
            {
                return BadRequest(_errorResources.CannotSetId());
            }

            var (identityResult, userId) = await _identityService.CreateUserAsync(user);
            var createdUser = await _identityService.GetUserAsync(userId.ToString());

            HttpClient client = new HttpClient();
            var registerConfiguration = _configuration.GetSection("RegisterConfiguration");
            string api = registerConfiguration.GetSection("EFApiBaseUrl").Value;
            var environment = registerConfiguration.GetSection("Environment").GetChildren();
            var locationId = environment.FirstOrDefault(x => x.Key == "LocationId").Value;
            string setupUser = $"{api}/user/{userId}/setuplocation";
            var userSetup = await client.PostAsJsonAsync(setupUser, locationId);
            userSetup.EnsureSuccessStatusCode();

            return CreatedAtAction(nameof(Get), new { Id = userId }, createdUser);
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] TUserDto user)
        {
            await _identityService.GetUserAsync(user.Id.ToString());
            await _identityService.UpdateUserAsync(user);

            return Ok();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(TKey id)
        {
            if (IsDeleteForbidden(id))
            {
                return StatusCode((int)System.Net.HttpStatusCode.Forbidden);
            }

            var user = new TUserDto { Id = id };

            await _identityService.GetUserAsync(user.Id.ToString());
            await _identityService.DeleteUserAsync(user.Id.ToString(), user);

            return Ok();
        }

        private bool IsDeleteForbidden(TKey id)
        {
            var userId = User.FindFirst(JwtClaimTypes.Subject);

            return userId == null ? false : userId.Value == id.ToString();
        }

        [HttpGet("{id}/Roles")]
        public async Task<ActionResult<UserRolesApiDto<TRoleDto>>> GetUserRoles(TKey id, int page = 1, int pageSize = 10)
        {
            var userRoles = await _identityService.GetUserRolesAsync(id.ToString(), page, pageSize);
            var userRolesApiDto = _mapper.Map<UserRolesApiDto<TRoleDto>>(userRoles);

            return Ok(userRolesApiDto);
        }

        [HttpPost("Roles")]
        public async Task<IActionResult> PostUserRoles([FromBody] UserRoleApiDto<TKey> role)
        {
            var userRolesDto = _mapper.Map<TUserRolesDto>(role);

            await _identityService.GetUserAsync(userRolesDto.UserId.ToString());
            await _identityService.GetRoleAsync(userRolesDto.RoleId.ToString());

            await _identityService.CreateUserRoleAsync(userRolesDto);

            return Ok();
        }

        [HttpDelete("Roles")]
        public async Task<IActionResult> DeleteUserRoles([FromBody] UserRoleApiDto<TKey> role)
        {
            var userRolesDto = _mapper.Map<TUserRolesDto>(role);

            await _identityService.GetUserAsync(userRolesDto.UserId.ToString());
            await _identityService.GetRoleAsync(userRolesDto.RoleId.ToString());
            await _identityService.DeleteUserRoleAsync(userRolesDto);

            return Ok();
        }

        //[HttpGet("{id}/Claims")]
        //public async Task<ActionResult<UserClaimsApiDto<TKey>>> GetUserClaims(TKey id, int page = 1, int pageSize = 20)
        //{
        //    var claims = await _identityService.GetUserClaimsAsync(id.ToString());
        //    var userClaimsApiDto = _mapper.Map<UserClaimsApiDto<TKey>>(claims);
        //    userClaimsApiDto.Claims = userClaimsApiDto.Claims.Where(x => x.OrganizationId == null).ToList();
        //    return Ok(userClaimsApiDto);
        //}

        [HttpGet("{id}/claims/person/{orgId?}")]
        public async Task<ActionResult<UserClaimApiDto<TKey>>> GetOrgUserClaimsById(Guid id, Guid? orgId = null)
        {
            List<Guid> userIds = new List<Guid>()
            {
                id
            };
            return Ok(await GetUserClaims(userIds, orgId));
        }

        [HttpPost("claims/people/{orgId?}")]
        public async Task<ActionResult<UserClaimApiDto<TKey>>> GetOrgUserClaimsById([FromBody] IEnumerable<Guid> ids, Guid? orgId = null)
        {
            return Ok(await GetUserClaims(ids, orgId));
        }

        private async Task<List<UserClaimApiDto<string>>> GetUserClaims(IEnumerable<Guid> ids, Guid? orgId = null)
        {
            using var connection = new SqlConnection(_dbContext.Database.GetDbConnection().ConnectionString);
            connection.Open();
            List<UserClaimApiDto<string>> returnData = new List<UserClaimApiDto<string>>();
            using var command = connection.CreateCommand();
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.AddWithValue("@Ids", CreateDataTable(ids));
            command.Parameters.AddWithValue("@OrgId", orgId);
            command.CommandText = "GetUserClaims";
            using var reader = await command.ExecuteReaderAsync();
            while (reader.Read())
            {
                returnData.Add(new UserClaimApiDto<string>
                               {
                                   ClaimId = reader.GetInt32(0),
                                   OrganizationId = reader[1] == DBNull.Value ? Guid.Empty : new Guid(reader[1].ToString()),
                                   ClaimType = reader[2] == DBNull.Value ? "" : reader.GetString(2),
                                   ClaimValue = reader[3] == DBNull.Value ? "" : reader.GetString(3),
                                   UserId = reader[4] == DBNull.Value ? "" : reader.GetString(4),
                               });
            }

            return returnData;
        }

        private static DataTable CreateDataTable(IEnumerable<Guid> ids)
        {
            DataTable table = new DataTable();
            table.Columns.Add("ID", typeof(Guid));
            foreach (Guid id in ids)
            {
                table.Rows.Add(id);
            }
            return table;
        }

        [HttpPost("Claims")]
        public async Task<IActionResult> PostUserClaims([FromBody] UserClaimApiDto<TKey> claim)
        {
            var userClaimDto = _mapper.Map<TUserClaimsDto>(claim);

            if (!userClaimDto.ClaimId.Equals(default))
            {
                return BadRequest(_errorResources.CannotSetId());
            }

            await _identityService.CreateUserClaimsAsync(userClaimDto);

            return Ok();
        }

        [HttpPut("Claims")]
        public async Task<IActionResult> PutUserClaims([FromBody] UserClaimApiDto<TKey> claim)
        {
            var userClaimDto = _mapper.Map<TUserClaimsDto>(claim);

            await _identityService.GetUserClaimAsync(userClaimDto.UserId.ToString(), userClaimDto.ClaimId);
            await _identityService.UpdateUserClaimsAsync(userClaimDto);

            return Ok();
        }

        [HttpPut("update/claim")]
        public async Task<IActionResult> UpdateUserClaim([FromBody] UserClaimApiDto<TKey> claim)
        {
            using var connection = new SqlConnection(_dbContext.Database.GetDbConnection().ConnectionString);
            connection.Open();
            List<UserClaimApiDto<string>> returnData = new List<UserClaimApiDto<string>>();
            using var cmd = new SqlCommand("UpdateUserClaim", connection);
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.AddWithValue("@ClaimId", claim.ClaimId);
            cmd.Parameters.AddWithValue("@ClaimValue", claim.ClaimValue);
            
            await cmd.ExecuteNonQueryAsync();
            return Ok();
        }

        [HttpDelete("{id}/Claims")]
        public async Task<IActionResult> DeleteUserClaims([FromRoute] TKey id, int claimId)
        {
            var userClaimsDto = new TUserClaimsDto
            {
                ClaimId = claimId,
                UserId = id
            };

            await _identityService.GetUserClaimAsync(id.ToString(), claimId);
            await _identityService.DeleteUserClaimAsync(userClaimsDto);

            return Ok();
        }

        [HttpGet("{id}/Providers")]
        public async Task<ActionResult<UserProvidersApiDto<TKey>>> GetUserProviders(TKey id)
        {
            var userProvidersDto = await _identityService.GetUserProvidersAsync(id.ToString());
            var userProvidersApiDto = _mapper.Map<UserProvidersApiDto<TKey>>(userProvidersDto);

            return Ok(userProvidersApiDto);
        }

        [HttpDelete("Providers")]
        public async Task<IActionResult> DeleteUserProviders([FromBody] UserProviderDeleteApiDto<TKey> provider)
        {
            var providerDto = _mapper.Map<TUserProviderDto>(provider);

            await _identityService.GetUserProviderAsync(providerDto.UserId.ToString(), providerDto.ProviderKey);
            await _identityService.DeleteUserProvidersAsync(providerDto);

            return Ok();
        }

        [HttpPost("ChangePassword")]
        public async Task<IActionResult> PostChangePassword([FromBody] UserChangePasswordApiDto<TKey> password)
        {
            var userChangePasswordDto = _mapper.Map<TUserChangePasswordDto>(password);
            await _identityService.UserChangePasswordAsync(userChangePasswordDto);

            return Ok();
        }

        [HttpGet("ConfirmEmail")]
        public async Task<IActionResult> ConfirmUserEmail([FromQuery] string userId)
        {

            if (userId == null)
            {
                return BadRequest();
            }
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return BadRequest();
            }

            var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);

            var result = await _userManager.ConfirmEmailAsync(user, code);
            if (result.Succeeded == true)
            {
                return Ok();
            }
            return BadRequest();
        }
        
        [HttpGet("getuser/{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            var user = await _userManager.FindByIdAsync(id.ToString());
            if (user == null)
            {
                return BadRequest();
            }
            return Ok(user);
        }

        [HttpGet("{id}/RoleClaims")]
        public async Task<ActionResult<RoleClaimsApiDto<TKey>>> GetRoleClaims(TKey id, string claimSearchText, int page = 1, int pageSize = 10)
        {
            var roleClaimsDto = await _identityService.GetUserRoleClaimsAsync(id.ToString(), claimSearchText, page, pageSize);
            var roleClaimsApiDto = _mapper.Map<RoleClaimsApiDto<TKey>>(roleClaimsDto);

            return Ok(roleClaimsApiDto);
        }

        [HttpGet("ClaimType/{claimType}/ClaimValue/{claimValue}")]
        public async Task<ActionResult<TUsersDto>> GetClaimUsers(string claimType, string claimValue, int page = 1, int pageSize = 10)
        {
            var usersDto = await _identityService.GetClaimUsersAsync(claimType, claimValue, page, pageSize);

            return Ok(usersDto);
        }

        [HttpGet("ClaimType/{claimType}")]
        public async Task<ActionResult<TUsersDto>> GetClaimUsers(string claimType, int page = 1, int pageSize = 10)
        {
            var usersDto = await _identityService.GetClaimUsersAsync(claimType, null, page, pageSize);

            return Ok(usersDto);
        }

        [HttpGet("search/{productOrgId}/productorgusers")]
        public async Task<ActionResult<TUsersDto>> SearchProductOrgUsersExclExisting(string productOrgId, string query = "")
        {
            using var connection = new SqlConnection(_dbContext.Database.GetDbConnection().ConnectionString);
            using var cmd = new SqlCommand("SearchProductOrgUsers_exclExisting", connection)
            {
                CommandType = CommandType.StoredProcedure
            };
            cmd.Parameters.AddWithValue("@ProductOrganizationId", productOrgId);
            cmd.Parameters.AddWithValue("@Query", query);

            connection.Open();
            List<object> returnData = new List<object>();
            using var reader = await cmd.ExecuteReaderAsync();
            while (reader.Read())
            {
                returnData.Add(new
                {
                    Id = new Guid((string)reader[0]),
                    RoleId = reader.GetGuid(1),
                    RoleName = reader[2] == DBNull.Value ? "" : reader.GetString(2),
                    Name = reader[3] == DBNull.Value ? "" : reader.GetString(3)
                });
            }

            return Ok(returnData);
        }

        [HttpGet("search/{instanceId}/instanceorgusers")]
        public async Task<ActionResult<TUsersDto>> SearchInstanceOrgUsersExclExisting(string instanceId, string query)
        {
            using var connection = new SqlConnection(_dbContext.Database.GetDbConnection().ConnectionString);
            using var cmd = new SqlCommand("SearchInstanceOrgUsers_exclExisting", connection)
            {
                CommandType = CommandType.StoredProcedure
            };
            cmd.Parameters.AddWithValue("@InstanceId", instanceId);
            cmd.Parameters.AddWithValue("@Query", query ?? "");

            connection.Open();
            List<object> returnData = new List<object>();
            using var reader = await cmd.ExecuteReaderAsync();
            while (reader.Read())
            {
                returnData.Add(new
                {
                    Id = new Guid((string)reader[0]),
                    RoleId = reader.GetGuid(1),
                    RoleName = reader[2] == DBNull.Value ? "" : reader.GetString(2),
                    Name = reader[3] == DBNull.Value ? "" : reader.GetString(3)
                });
            }

            return Ok(returnData);
        }

        [HttpGet("search/{instanceId}/rowinstanceusers")]
        public async Task<ActionResult<TUsersDto>> SearchRowInstanceUsersExclExisting(string instanceId, string rowId, string query)
        {
            using var connection = new SqlConnection(_dbContext.Database.GetDbConnection().ConnectionString);
            using var cmd = new SqlCommand("SearchRowInstanceUsers_exclExisting", connection)
            {
                CommandType = CommandType.StoredProcedure
            };
            cmd.Parameters.AddWithValue("@InstanceId", instanceId);
            cmd.Parameters.AddWithValue("@RowId", rowId);
            cmd.Parameters.AddWithValue("@Query", query ?? "");

            connection.Open();
            List<object> returnData = new List<object>();
            using var reader = await cmd.ExecuteReaderAsync();
            while (reader.Read())
            {

                returnData.Add(new
                {
                    Id = new Guid((string)reader[0]),
                    RoleId = reader.GetGuid(1),
                    RoleName = reader[2] == DBNull.Value ? "" : reader.GetString(2),
                    Name = reader[3] == DBNull.Value ? "" : reader.GetString(3)
                });

            }

            return Ok(returnData);
        }

        [HttpGet("search/{orgId}/orgusers")]
        public async Task<ActionResult<TUsersDto>> SearchOrgUsersExclExisting(string orgId, string query)
        {
            using var connection = new SqlConnection(_dbContext.Database.GetDbConnection().ConnectionString);
            using var cmd = new SqlCommand("SearchOrganizationUsers_exclExisting", connection)
            {
                CommandType = CommandType.StoredProcedure
            };
            cmd.Parameters.AddWithValue("@OrganizationId", orgId);
            cmd.Parameters.AddWithValue("@Query", query ?? "");

            connection.Open();
            List<object> returnData = new List<object>();
            using var reader = await cmd.ExecuteReaderAsync();
            while (reader.Read())
            {
                returnData.Add(new
                {
                    Id = new Guid((string)reader[0]),
                    Name = reader[1] == DBNull.Value ? "" : reader.GetString(1)
                });
            }

            return Ok(returnData);
        }

        [HttpGet("search/currentamount/{currentAmount}/getamount/{getAmount}")]
        public async Task<ActionResult<TUsersDto>> GetAllOrganizationUsers(int currentAmount, int getAmount, string repoSearchValue)
        {
            using var connection = new SqlConnection(_dbContext.Database.GetDbConnection().ConnectionString);
            using var cmd = new SqlCommand("SearchAllOrganizationUserClaims", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            cmd.Parameters.AddWithValue("@Query", repoSearchValue ?? "");
            cmd.Parameters.AddWithValue("@CurrentAmount", currentAmount);
            cmd.Parameters.AddWithValue("@GetAmount", getAmount);

            connection.Open();
            List<object> returnData = new List<object>();
            using var reader = await cmd.ExecuteReaderAsync();

            while (reader.Read())
            {
                returnData.Add(new
                {
                    Id = reader.GetGuid(0),
                    TableId = reader.GetGuid(0),
                    Name = reader[1] == DBNull.Value ? "" : reader.GetString(1),
                    FamilyName = reader[2] == DBNull.Value ? "" : reader.GetString(2),
                    GivenName = reader[3] == DBNull.Value ? "" : reader.GetString(3),
                    Email = reader[4] == DBNull.Value ? "" : reader.GetString(4),
                    Country = reader[5] == DBNull.Value ? "" : reader.GetString(5),
                    PostalCode = reader[6] == DBNull.Value ? "" : reader.GetString(6),
                    PhoneNumber = reader[7] == DBNull.Value ? "" : reader.GetString(7),
                });
            }

            return Ok(returnData);
        }

        [HttpGet("lookup/email")]
        public async Task<ActionResult<TUserDto>> GetUserByEmail([FromQuery] string email = "")
        {
            TUserDto returnData = new TUserDto();

            if (email != null)
            {
                var user = await _userManager.FindByEmailAsync(email);
                if (user != null)
                {

                    returnData.UserName = user.UserName ?? null;
                    returnData.Email = user.Email;
                    return Ok(returnData);
                }
            }

            return Ok(returnData);
        }

        [HttpGet("lookup/username")]
        public async Task<ActionResult<TUserDto>> GetUserByUsername([FromQuery] string username = "")
        {
            TUserDto returnData = new TUserDto();

            if (username != null)
            {
                var user = await _userManager.FindByNameAsync(username);
                if (user != null)
                {

                    returnData.UserName = user.UserName ?? null;
                    returnData.Email = user.Email;
                    return Ok(returnData);
                }

            }

            return Ok(returnData);
        }
    }
}
