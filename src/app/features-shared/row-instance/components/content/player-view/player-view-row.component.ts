import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, signal, SimpleChanges } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { BannerService } from '@app/core/services/banner.service';
import { IContent, IEngagementIn, IInstance, IRouteParams, IRow, IRowContent, IRowContentById, IUserContext } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { EngagementTypes } from '@app/core/enums/engagment-types.enum';
import { EntityTypes } from '@app/core/enums/entity-type';
import { RowType } from '@app/core/enums/row-type.enum';
import { ViewType } from '@app/core/enums/view-type';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RowService } from '@app/core/services/row.service';
import { Subject, takeUntil } from 'rxjs';
import { PlayerViewSidePanelSelectionService } from '@app/core/services/player-side-panel-selection.service';

@Component({
  selector: 'app-player-view-row',
  templateUrl: './player-view-row.component.html',
  styleUrls: ['./player-view-row.component.scss'],
  standalone: false,
})
export class PlayerViewRowComponent implements OnInit, OnDestroy, OnChanges {
  @Input() reload$: Subject<string>;
  @Input() row: IRow;
  @Input() instance: IInstance;
  @Input() readingMode: boolean;
  @Input() searchFilter: string;
  @Input() routeParams: IRouteParams;
  @Input() instanceComponentId?: string | null;
  @Input() isPlayerSidePanel = false;
  @Input() selectedUserId: string;
  @Input() playerSidePanel = false;
  @Output() selectedChanged = new EventEmitter<{ event: string | number; actionBw: ActionTypes | undefined }>();
  @Output() rowContentLoaded = new EventEmitter();
  componentDestroyed$: Subject<boolean> = new Subject();
  rowContent: IRowContent;
  limit = 15;
  skip = 0;
  showLoadMore = true;
  rowTypes = RowType;
  entityTypes = EntityTypes;
  actionTypes = ActionTypes;
  expanded = signal(false);
  currentContentId: string = '';
  showGradingView = false;

  constructor(
    private eventService: Events,
    private dataService: DataService,
    public activatedRoute: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private rowService: RowService,
    private instanceService: InstanceService,
    private playerViewSidePanelSelectionService: PlayerViewSidePanelSelectionService,
    private globalToastComponent: GlobalToastService,
    private bannerService: BannerService
  ) {
    this.rowContent = { content: [] };
  }

  ngOnInit() {
    this.showGradingView = sessionStorage.getItem('showGradingView') === 'true';
    if (this.selectedUserId) {
      this.expanded.set(true);
    }

    this.router.events.subscribe(event => {
      if (!this.router.url.includes('player')) {
        // Hide both toast and banner when navigating away from player
        if (this.globalToastComponent) {
          this.globalToastComponent.toast?.dismiss();
        }
        this.bannerService.hideBanner();
      }
    });

    this.reload$.pipe(takeUntil(this.componentDestroyed$)).subscribe((id: string) => {
      if (id === this.row.id) {
        this.getRowContent(this.limit, this.skip, this.searchFilter);
      }
    });

    this.eventService.subscribe('instanceInProgress', (instanceId: string) => {
      if (this.rowContent.content?.some(x => x.id === instanceId)) {
        const index = this.rowContent.content.findIndex(x => x.id === instanceId);
        this.rowContent.content[index].status = 'InProgress';
      }
    });

    this.eventService.subscribe('instanceCompleted', (event: { instanceId: string; locked?: boolean }) => {
      if (this.rowContent.content?.some(x => x.id === event.instanceId) && this.expanded() == true) {
        const index = this.rowContent.content.findIndex(x => x.id === event.instanceId);
        const lastIndex = this.rowContent.content.length - 1;

        if (event.locked) {
          if (index === lastIndex) {
            this.eventService.publish('playerRowCompleted', this.row.id);
          } else {
            const nextContentItem = this.rowContent.content[index + 1];
            this.selectedChanged.emit({ event: nextContentItem.slug ?? nextContentItem.id, actionBw: nextContentItem.actionBW });
            this.playerViewSidePanelSelectionService.setSelectedContentId(nextContentItem.id);
            this.playerViewSidePanelSelectionService.setSelectedContentRowId(this.row.id);

            this.checkRowContent();
          }
          return;
        }

        if (index === lastIndex) {
          //check for more content before completing the row
          if (this.showLoadMore) {
            // load more content
            this.skip += 15;
            const moreContent = this.getRowContentObservable(this.limit, this.skip, this.searchFilter);
            moreContent.pipe(takeUntil(this.componentDestroyed$)).subscribe(rowContent => {
              this.row.hidden = !(rowContent?.content?.length > 0) && !(this.rowContent?.content?.length > 0) && this.readingMode === true && this.routeParams?.viewType !== ViewType.Builder;
              if (rowContent?.content?.length < this.limit) {
                this.showLoadMore = false;
              }

              rowContent.content = rowContent.content.filter(x => x.entityType === this.entityTypes.Instances && x.featureType !== 'Landing Pages');
              if (rowContent?.content && rowContent.content.length > 0) {
                this.rowContent = rowContent;

                // select the next item and load it
                const nextContentItem = rowContent.content[0];
                this.selectedChanged.emit({ event: nextContentItem.slug ?? nextContentItem.id, actionBw: nextContentItem.actionBW });
                this.playerViewSidePanelSelectionService.setSelectedContentId(nextContentItem.id);
                this.playerViewSidePanelSelectionService.setSelectedContentRowId(this.row.id);

                this.checkRowContent();
              }

              this.rowContent.content = [...this.rowContent.content];
            });
            return;
          }
          // complete the row
          this.rowContent.content[index].status = 'Completed';
          this.eventService.publish('playerRowCompleted', this.row.id);
        } else {
          const nextContentItem = this.rowContent.content[index + 1];
          this.selectedChanged.emit({ event: nextContentItem.slug ?? nextContentItem.id, actionBw: nextContentItem.actionBW });
          this.playerViewSidePanelSelectionService.setSelectedContentId(nextContentItem.id);
          this.playerViewSidePanelSelectionService.setSelectedContentRowId(this.row.id);
          this.rowContent.content[index].status = 'Completed';
          this.checkRowContent();
        }
      }
    });

    this.eventService.subscribe('playerRowSelected', (rowId: string) => {
      if (this.row.id === rowId) {
        const firstContentItem = this.rowContent.content[0];

        if (firstContentItem) {
          this.selectedChanged.emit({ event: firstContentItem.slug ?? firstContentItem.id, actionBw: firstContentItem.actionBW });
          this.playerViewSidePanelSelectionService.setSelectedContentId(firstContentItem.id);
          this.playerViewSidePanelSelectionService.setSelectedContentRowId(this.row.id);
        } else {
          this.eventService.publish('playerRowCompleted', this.row.id);
        }
      }
    });

    this.rowService.firstUngradedRowLoad$.pipe(takeUntil(this.componentDestroyed$)).subscribe((id: string) => {
      if (this.expanded() === true && this.selectedUserId) {
        const firstUngradedInstance = this.rowContent.content.find(x => x.containsGrading === true && x.isGraded === false && x.status === 'Completed');
        if (firstUngradedInstance) {
          this.playerViewSidePanelSelectionService.setSelectedContentId(firstUngradedInstance.id);
          this.playerViewSidePanelSelectionService.setSelectedContentRowId(this.row.id);
        } else if (this.rowContent?.content) {
          this.playerViewSidePanelSelectionService.setSelectedContentId(this.rowContent.content[0]?.id);
          this.playerViewSidePanelSelectionService.setSelectedContentRowId(this.row.id);
        }
      }
    });

    this.playerViewSidePanelSelectionService.selectedContentRowId$.pipe(takeUntil(this.componentDestroyed$)).subscribe(selectedRowId => {
      const shouldExpand = selectedRowId === this.row.id;
      this.expanded.set(shouldExpand);
    });

    this.reload$.next(this.row.id);
  }

  checkRowContent() {
    if (this.playerViewSidePanelSelectionService.selectedContentRowId === this.row.id) {
      if (this.rowContent.rowComplete) {
        // Use banner service instead of toast for completed all items
        this.bannerService.showCompletedAllBanner(this.instance);
      } else if (this.rowContent?.totalComplete && this.rowContent.totalComplete > 0 && this.rowContent.minValue) {
        // Use banner service instead of toast for completed some items
        this.bannerService.showCompletedSomeBanner(this.rowContent.totalComplete, this.rowContent.minValue, this.instance);
      } else if (this.rowContent.minValue) {
        // Use banner service instead of toast for minimum required items
        this.bannerService.showMinimumRequiredBanner(this.rowContent.minValue);
      }
      this.expanded.set(true);
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['searchFilter']) {
      this.rowContent.content = [];
      this.skip = 0;
      this.reload$.next(this.row.id);
    }
  }

  checkContainsGrading() {
    return this.rowContent.content?.some(x => x.containsGrading === true && x.status === 'Completed');
  }

  checkGraded() {
    return !this.rowContent.content.filter(x => x.containsGrading === true && x.status === 'Completed')?.some(x => x.isGraded === false);
  }

  getRowContentObservable(limit: number, skip: number, filter?: string) {
    const userLocation = { ...this.authService.userContext } as IUserContext;
    return this.dataService.getRowContent(
      this.row.id,
      userLocation,
      limit,
      skip,
      false,
      filter,
      this.instance?.id,
      this.instanceService.getPrevInstanceSlug(),
      this.instance?.feature.featureType.id,
      this.selectedUserId
    );
  }

  getRowContent(limit: number, skip: number, filter?: string) {
    const userLocation = { ...this.authService.userContext } as IUserContext;
    this.dataService
      .getRowContent(this.row.id, userLocation, limit, skip, false, filter, this.instance?.id, this.instanceService.getPrevInstanceSlug(), this.instance?.feature.featureType.id, this.selectedUserId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((rowContent: IRowContent) => {
        alert(rowContent?.content?.length);
        this.row.hidden = !(rowContent?.content?.length > 0) && !(this.rowContent?.content?.length > 0) && this.readingMode === true && this.routeParams?.viewType !== ViewType.Builder;
        if (rowContent?.content?.length < limit) {
          this.showLoadMore = false;
        }

        if (rowContent?.content && rowContent.content.length > 0) {
          this.rowContent = rowContent;

          //PlayerView = EntityTypes Of Instances.
          rowContent.content = rowContent.content.filter(x => x.entityType === this.entityTypes.Instances && x.featureType !== 'Landing Pages');
          if (rowContent.content.length > 0) {
            rowContent.content.forEach(res => {
              if (this.rowContent.content.findIndex(x => x.id === res.id) === -1) {
                this.rowContent.content.push(res);
              }
            });

            if (this.rowContent.content && this.rowContent.content.length > 0) {
              if (!this.routeParams?.instanceSlug || this.routeParams?.instanceSlug === 'default' || this.router.url.indexOf('scorm') !== -1) {
                this.selectedChanged.emit({ event: this.rowContent.content[0]?.slug ?? this.rowContent.content[0]?.id, actionBw: this.rowContent.content[0]?.actionBW });
                this.playerViewSidePanelSelectionService.setSelectedContentId(this.rowContent.content[0]?.id);
                this.playerViewSidePanelSelectionService.setSelectedContentRowId(this.row.id);
              } else {
                if (this.row.id === sessionStorage.getItem('rowId')) {
                  const rowContentItem = this.rowContent.content.find(x => x.id === this.routeParams?.instanceSlug || x.slug === this.routeParams?.instanceSlug);

                  if (rowContentItem) {
                    this.setSelected(this.routeParams.instanceSlug, rowContentItem, rowContentItem?.actionBW);
                  } else {
                    // If the rowContentItem does not exist, load more content until it is found
                    this.skip += 15;
                    this.reload$.next(this.row.id);
                  }
                } else {
                  // If the row id is not provided, then do general search on all rows
                  const rowContentItem = this.rowContent.content.find(x => x.id === this.routeParams?.instanceSlug || x.slug === this.routeParams?.instanceSlug);

                  if (rowContentItem) {
                    this.setSelected(this.routeParams.instanceSlug, rowContentItem, rowContentItem?.actionBW);
                  }
                }
              }
            }
            this.rowContent.content = [...this.rowContent.content];
          }
        }

        if (skip === 0) {
          this.rowContentLoaded.next(this.checkContainsGrading() && !this.checkGraded());
        }
      });
  }

  setSelected(selected: string, item: IContent | undefined, actionBw?: ActionTypes) {
    if (item) {
      if (this.playerViewSidePanelSelectionService.selectedContentId !== item?.id) {
        this.playerViewSidePanelSelectionService.setSelectedContentId(item?.id ?? selected);
        this.playerViewSidePanelSelectionService.setSelectedContentRowId(this.row.id);
        this.checkRowContent();
      }
    }

    if (item?.entityType === EntityTypes.Instances) {
      this.eventService.publish('isLastRowContentItem', this.checkIsLastContentItem(item.id));
      this.selectedChanged.emit({ event: selected, actionBw: actionBw });

      this.dataService
        .addInstanceEngagement({ instanceId: item.id, engagementType: EngagementTypes.Click, nominalValue: 1, percentageValue: 100 } as IEngagementIn)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {});
    }
  }

  checkIsLastContentItem(itemId: string) {
    const content = this.rowContent.content.map((value, index) => ({ ...value, contentOrder: index }) as IContent);
    const max = Math.max(...content.map(x => x.contentOrder ?? 0));

    const itemIndex = content.find(x => x.id === itemId)?.contentOrder;

    return max === itemIndex;
  }

  loadMore() {
    this.skip += 15;
    this.reload$.next(this.row.id);
  }

  ngOnDestroy() {
    this.globalToastComponent.toast?.dismiss();
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
