ion-item {
  border: 1px solid #4e4e4e;
  border-radius: 5px;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: white;
  font-size: 16px;
  --padding-start: 8px !important;
  caret-color: #7f550c;
  background-color: var(--background-color);
}

.input {
  border: 1px solid #4e4e4e;
  border-radius: 5px;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: white;
  font-size: 16px;
  --padding-start: 8px !important;
  caret-color: #7f550c;
  background-color: var(--background-color);
}

.scale-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  justify-content: space-between;

  ion-label {
    height: fit-content;
    margin-left: 5px;
    margin-right: 5px;
  }
}

.top-label {
  margin-bottom: 0.2em;
  margin-left: 20px;
  color: white;
}

.label {
  color: white;
}

.container {
  margin-top: 20px;
}

.full-width {
  width: 100%;
}

.center-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  ion-label {
    margin-top: 18px;
    height: 37px;
    text-align: center;
    line-height: 37px;
  }
}

.col-padding {
  padding-right: 10px;
}

ion-card {
  border: 1px solid #4e4e4e;
  border-radius: 5px;
  margin-top: 0.5em;
  color: white;
  font-size: 16px;
  --padding-start: 8px !important;
  caret-color: #7f550c;
  background-color: var(--background-color);
}

ion-card-content {
  padding-right: 0 !important;
}

ion-card-content {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.alphabet {
  border: 1px solid #aaaaaa;
  border-radius: 5px;
  font-size: 20px;
  font-weight: bold;
  width: auto;
  text-align: center;
  background-color: #aaaaaa;
  color: black;
  margin: 5px;
  margin-left: -10px;
  min-width: 30px;
}

.center-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  //height: 100%;
  justify-content: center;
  width: 100%;
}

ion-grid {
  ion-row {
    width: 100%;
  }
}
