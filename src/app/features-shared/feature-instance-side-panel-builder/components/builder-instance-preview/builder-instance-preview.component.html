<ion-content class="preview-parent-content-container" [scrollEvents]="true">
  @if (hasEditAccess() && !panelState) {
    <div class="edit-button">
      <ion-button fill="clear" class="inner-container" (click)="setupStepsOnEdit()">
        <ion-icon name="pencil"></ion-icon>
        Edit
      </ion-button>
    </div>
  }
  @for (instanceSection of template.instanceSections; track trackByJsonObject(i, instanceSection); let i = $index) {
    <div class="section-container">
      @if (
        (editPanelBuilderIn?.id === instanceSection.section?.id || editPanelBuilderIn?.id === instanceSection.id) &&
        !disabled &&
        instanceSection.section?.title &&
        !(instanceSection.section?.typeBw === sectionTypes.Dynamic && instanceSection.section?.templateId)
      ) {
        <ion-label class="header section-title">
          {{ instanceSection.section?.title && instanceSection.section.title.length > 0 ? instanceSection.section.title : 'Section' }}
        </ion-label>
      }
      @if (editPanelBuilderIn?.id === instanceSection.instanceSectionComponents?.[0]?.component?.id && !disabled) {
        <ion-label class="header section-title">
          {{ instanceSection.instanceSectionComponents?.[0]?.component?.templateField?.label ?? instanceSection.instanceSectionComponents?.[0]?.component?.componentType?.name }}
        </ion-label>
      }
      @if (!(instanceSection.section?.typeBw === sectionTypes.Dynamic && instanceSection.section?.templateId)) {
        <ion-card
          [ngClass]="
            (editPanelBuilderIn?.id === instanceSection.section?.id || editPanelBuilderIn?.id === instanceSection.id) && !disabled ? 'preview-parent-container selected' : 'preview-parent-container'
          "
          [style]="
            instanceSection?.section?.hideBackground !== true && (instanceSection?.section?.backgroundColor || instanceSection.backgroundColor)
              ? 'background:' + (instanceSection?.backgroundColor && instanceSection.backgroundColor.length > 0 ? instanceSection.backgroundColor : instanceSection?.section?.backgroundColor ?? 'none')
              : 'background-color:none'
          "
          (click)="editSection(instanceSection.section, $event)"
          [id]="instanceSection.section?.id || ''">
          <ion-grid>
            @if (instanceSection.section?.showTitleOnPlayer === true) {
              <ion-row>
                <app-heading-value
                  [inheritedPropertyValue]="instanceSection?.title || instanceSection.section?.title ? (instanceSection?.title ?? instanceSection.section?.title) : 'Section'"
                  [fontSize]="22"></app-heading-value>
              </ion-row>
            }
            @if (instanceSection.section?.showDescOnPlayer === true) {
              <ion-row>
                <app-text-value [defaultValue]="instanceSection.section?.description ? instanceSection.section.description : 'Description'"></app-text-value>
              </ion-row>
            }
            @for (
              groupedComponents of instanceSection.instanceSectionComponents | groupBy: 'component.builderRowNumber' | values;
              track trackByJsonObject(compIndex, groupedComponents);
              let compIndex = $index
            ) {
              <ion-row [ngStyle]="getSectionMaxWidthStyle(instanceSection.section, instanceSection.id, groupedComponents[0]?.component?.builderRowNumber)">
                @for (instanceComponent of groupedComponents | orderBy: 'component.templateField.colNumber'; track trackByJsonObject($index, instanceComponent)) {
                  @if (instanceComponent.component?.componentType?.name === 'Listing Details' && i === 0 && compIndex === 0 && instanceComponent.component?.templateField?.moveToBack !== true) {
                    <div class="image-header-gradient" [style]="'--background-image:url(' + assetUrl + ');'"></div>
                  }
                  @if (
                    instanceComponent.component?.componentType?.name !== 'banner' && !instanceComponent.component?.templateField?.isFilter && instanceComponent.component?.templateField?.isPreviewField
                  ) {
                    <ion-col
                      col-12
                      col-md-6
                      col-lg-4
                      col-xl-3
                      [size]="instanceComponent.component?.templateField?.colspan !== 0 ? instanceComponent.component?.templateField?.colspan : null"
                      [id]="instanceComponent.component?.id || ''">
                      <!-- DO NOT SHOW OVERLAY FOR QR CODE -->
                      @if (
                        !(instanceComponent?.component?.componentType?.name === 'Text' && instanceComponent?.component?.templateField?.systemProperty?.property === 'Campaign.CampaignCode') &&
                        instanceComponent?.component?.componentType?.name !== 'User RIASEC Score Chart'
                      ) {
                        <div
                          [ngClass]="editPanelBuilderIn?.id === instanceComponent.component?.id && !disabled ? 'component-overlay selected' : 'component-overlay'"
                          (click)="editComponent(instanceComponent.component, $event)">
                          @if (editPanelBuilderIn?.id === instanceComponent.component?.id && !disabled) {
                            <ion-label class="header title">
                              {{ instanceComponent?.component?.templateField?.label ?? instanceComponent?.component?.componentType?.name }}
                            </ion-label>
                          }
                        </div>
                      }
                      @if (!instanceComponent.component?.templateField?.isFilter && instanceComponent.component?.templateField?.isPreviewField) {
                        <app-component-row-selector
                          [instance]="instance"
                          [instanceSectionComponent]="instanceComponent"
                          [instanceSection]="instanceSection"
                          [routeParams]="routeParams"
                          [sidePanelPadding]="true"
                          [searchFilter]="searchFilter"
                          [continuousFeedback]="instanceSection?.section?.isContinuousFeedback ?? false">
                        </app-component-row-selector>
                      }
                    </ion-col>
                  }
                }
              </ion-row>
            }
          </ion-grid>
          @if (instanceSection?.section?.typeBw === sectionTypes.Dynamic && !disabled) {
            <div class="add-component-container">
              <div class="inner-content">
                <div class="add-button-container">
                  <div class="icon-container">
                    <ion-icon (click)="addComponent(instanceSection, $event)" name="add-circle-outline"></ion-icon>
                  </div>
                  <div class="button-heading">Add a component</div>
                </div>
              </div>
            </div>
          }
        </ion-card>
      }
      @if (
        instanceSection?.section?.typeBw === sectionTypes.Dynamic &&
        !disabled &&
        !(template.instanceSections[i + 1]?.section?.typeBw === sectionTypes.Dynamic && template?.instanceSections[i + 1]?.section?.templateId)
      ) {
        <div class="section-add-line">
          <div class="icon-container">
            <hr class="start" />
            <ion-icon (click)="addSection(i)" name="add-circle-outline"></ion-icon>
            <hr class="end" />
          </div>
        </div>
      }
    </div>
  }
</ion-content>
