@if (hasRoleAccess()) {
  <ion-row class="view-options-row">
    <div class="top-header">
      <ion-button (click)="openSelectAchievementType($event)" color="warning">Add</ion-button>
    </div>
  </ion-row>
}
<ion-row>
  @if (!achievements || achievements?.length === 0) {
    <app-empty-default-assignment [title]="'Add an achievement'" [description]="'Reward people who complete your assignment'"></app-empty-default-assignment>
  }
  @for (achievement of achievements; track achievement) {
    <app-achievement-control [achievementInstance]="achievement.achievementInstance" [itemBackgroundColor]="controlBackground"></app-achievement-control>
  }
</ion-row>
