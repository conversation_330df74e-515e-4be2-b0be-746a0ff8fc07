import { HttpEventType } from '@angular/common/http';
import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { IFileOut, IQuestion } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { environment } from '@env/environment';
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
import { Subject } from 'rxjs/internal/Subject';
import { takeUntil, tap } from 'rxjs/operators';

@Component({
    selector: 'app-settings-panel',
    templateUrl: './settings-panel.component.html',
    styleUrls: ['./settings-panel.component.scss'],
    standalone: false
})
export class SettingsPanelComponent extends BaseControlComponent implements OnDestroy {
  @Input() backgroundColor = '#181818';
  @Input() question: IQuestion;
  @Output() questionChanged = new EventEmitter<any>();
  @Output() questionBackgroundChanged = new EventEmitter<string>();
  assetUrl: string | null;
  componentDestroyed$: Subject<boolean> = new Subject();
  file: File | null = null;
  placeHolderText: string;
  progress = new BehaviorSubject<number>(0);
  constructor(private dataService: DataService) {
    super();
  }

  override setValue(event: any): void {
    this.writeValue(event);
  }

  questionUpdated() {
    this.questionChanged.emit(null);
  }

  fileBrowseHandler(event: any) {
    if (event?.target?.files) {
      this.processFile(event.target.files[0]);
    }
  }

  processFile(file: File) {
    if (this.validateFileFormat(file)) {
      this.file = file;
      const formData = new FormData();
      formData.append('file', file);

      const asset: IFileOut = {
        asset: formData,
        type: file.type,
      };

      this.uploadAsset(asset).pipe(takeUntil(this.componentDestroyed$)).subscribe();
    }
  }

  validateFileFormat(file: File): boolean {
    const matches = file.type.match('image/*');
    if (matches && matches.length > 0) {
      return true;
    }
    return false;
  }

  uploadAsset(asset: IFileOut) {
    return this.dataService.postAsset(asset, 'static', null).pipe(
      tap(event => {
        if (event.type === HttpEventType.UploadProgress) {
          if (event.total) {
            this.progress.next(Math.round((event.loaded / event.total) * 100));
          }
        } else if (event.type === HttpEventType.Response && event.body) {
          this.setValue(event.body.id);
          this.question.backgroundImageAssetId = event.body.id;
          this.assetUrl = `${environment.contentUrl}asset/${this.textValue}/content`;
          this.questionBackgroundChanged.emit(this.question.backgroundImageAssetId);
        }
      })
    );
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
