import { AuthoringHeaderComponent } from '@app/standalone/components/authoring-header/authoring-header.component';
import { FileUploadControlComponent } from '@app/standalone/components/file-upload-control/file-upload-control.component';
import { TagTreeNavigationSelectorComponent } from '@app/standalone/components/tag-tree-navigation-selector/tag-tree-navigation-selector.component';
import { DropDownQuestionComponent } from './components/drop-down-question/drop-down-question.component';
import { FileUploadQuestionComponent } from './components/file-upload-question/file-upload-question.component';
import { MatchingListComponent } from './components/matching-list/matching-list.component';
import { MatchingQuestionBaseComponent } from './components/matching-question-base/matching-question-base.component';
import { MultipleChoiceAnswerComponent } from './components/multiple-choice-answer/multiple-choice-answer.component';
import { MultipleChoiceQuestionComponent } from './components/multiple-choice-question/multiple-choice-question.component';
import { OpinionScaleAnswerComponent } from './components/opinion-scale-answer/opinion-scale-answer.component';
import { OpinionScaleQuestionComponent } from './components/opinion-scale-question/opinion-scale-question.component';
import { OptionPhotoAnswerComponent } from './components/option-photo-answer/option-photo-answer.component';
import { OptionPhotoQuestionComponent } from './components/option-photo-question/option-photo-question.component';
import { PictureMatchingComponent } from './components/picture-matching/picture-matching.component';
import { PolarAnswerComponent } from './components/polar-answer/polar-answer.component';
import { PolarQuestionComponent } from './components/polar-question/polar-question.component';
import { QuestionActiveComponent } from './components/question-active/question-active.component';
import { QuestionAnswerTagComponent } from './components/question-answer-tag/question-answer-tag.component';
import { RiasecInterestProfilerComponent } from './components/riasec-interest-profiler/riasec-interest-profiler.component';
import { TextQuestionComponent } from './components/text-question/text-question.component';
import { TextToPictureMatchingComponent } from './components/text-to-picture-matching/text-to-picture-matching.component';

export const featureComponents: any[] = [
  QuestionActiveComponent,
  MultipleChoiceQuestionComponent,
  OptionPhotoQuestionComponent,
  OpinionScaleQuestionComponent,
  PolarQuestionComponent,
  TextQuestionComponent,
  DropDownQuestionComponent,
  MatchingQuestionBaseComponent,
  FileUploadQuestionComponent,
  MultipleChoiceAnswerComponent,
  OpinionScaleAnswerComponent,
  OptionPhotoAnswerComponent,
  PictureMatchingComponent,
  PolarAnswerComponent,
  PolarQuestionComponent,
  TextToPictureMatchingComponent,
  MatchingListComponent,
  RiasecInterestProfilerComponent,
  QuestionAnswerTagComponent,
];

export const standaloneComponents: any[] = [FileUploadControlComponent, AuthoringHeaderComponent, TagTreeNavigationSelectorComponent];
