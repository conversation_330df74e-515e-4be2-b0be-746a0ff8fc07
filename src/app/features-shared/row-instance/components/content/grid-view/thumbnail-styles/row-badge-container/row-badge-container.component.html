<div class="achievements-parent-container" (click)="route()">
  @if (status === null || status === undefined || status === 'NotStarted') {
    <ion-card class="parent-container-notstarted">
      <ion-row>
        <ion-col class="img-col" size="4"><img [src]="imageUrl" /></ion-col>
        <ion-col size="8">
          <div class="heading-container">
            <div class="top-description">
              <div class="title">{{ instanceName }}</div>
            </div>
            <div class="issued-by">
              <span class="descripitor" [innerHTML]="featureDescriptor ? '. ' : ''"></span>
              Issued by: <span class="inner-sub">{{ organizationName }}</span>
            </div>
          </div>
          <div class="content-container">
            {{ description }}
          </div>
        </ion-col>
      </ion-row>
    </ion-card>
  }
  @if (status === 'InProgress') {
    <ion-card class="parent-container-inprogress" [ngStyle]="!isEnrolled ? { 'border-color': 'orange' } : { 'border-color': 'green' }">
      <ion-row>
        <ion-col class="img-col" size="4"><img [src]="imageUrl" /></ion-col>
        <ion-col size="8">
          <div class="heading-container">
            <div class="top-description">
              <div class="title">{{ instanceName }}</div>
            </div>
            <div class="issued-by">
              <span class="descripitor" [innerHTML]="featureDescriptor ? '. ' : ''"></span>
              Issued by: <span class="inner-sub">{{ organizationName }}</span>
            </div>
          </div>
          <div class="content-container">
            {{ description }}
          </div>
          <div class="progress-bar-container">
            <app-chevron-progress-bar [chevAmount]="10" [progress]="progress" [isEnrolled]="isEnrolled"></app-chevron-progress-bar>
          </div>
          <div class="finish-button-container"><ion-button fill="clear" color="black">Finish</ion-button></div>
        </ion-col>
      </ion-row>
    </ion-card>
  }
  @if (status === 'InProgress') {
    <div class="in-progress-foot-container">
      <span class="inner" [ngStyle]="!isEnrolled ? { 'background-color': 'rgb(172,126,1)' } : { 'background-color': 'green' }">IN PROGRESS</span>
    </div>
  }
  @if (status === 'Completed') {
    <ion-card class="parent-container-completed">
      <ion-row>
        <ion-col class="img-col" size="4"><img [src]="imageUrl" /></ion-col>
        <ion-col size="8">
          <div class="heading-container">
            <div class="top-description">
              <div class="title">{{ instanceName }}</div>
              <div class="icon-container"><ion-icon name="checkmark-circle-outline"></ion-icon></div>
            </div>
            <div class="sub-description">
              <span class="earned-container">EARNED</span><span class="inner-sub">{{ setLastModifiedDate | date: 'MMMM d, y' }}</span>
            </div>
            <div class="issued-by">
              <span class="descripitor" [innerHTML]="featureDescriptor ? '. ' : ''"></span>
              Issued by: <span class="inner-sub">{{ organizationName }}</span>
            </div>
          </div>
        </ion-col>
      </ion-row>
    </ion-card>
  }
  @if (status === 'Completed') {
    <div class="completed-foot-container">
      <span class="inner">COMPLETED</span>
    </div>
  }
</div>
