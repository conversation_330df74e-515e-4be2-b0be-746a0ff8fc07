import { Component, Input, OnInit } from '@angular/core';
import { AuthService } from '@app/core/services/auth-service';
import { environment } from '@env/environment';
import { ThumbnailStyleBaseComponent } from '../../base/thumbnail-style-base.component';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { ModalController } from '@ionic/angular';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-row-photo-block',
    templateUrl: './photo-block.component.html',
    styleUrls: ['./photo-block.component.scss'],
    standalone: false
})
export class PhotoBlockComponent extends ThumbnailStyleBaseComponent implements OnInit {
  @Input() title: string;
  @Input() description: string;
  @Input() iconAssetId: string;
  @Input() externalLogoUrl?: any;
  @Input() isEducator: boolean = false;
  @Input() hasAdminAccess: boolean = false;
  @Input() hideAddButton: boolean = false;
  @Input() actionUrl: string;
  @Input() action: string;
  @Input() isAssignmentRow?: boolean = false;

  constructor(dataService: DataService, authService: AuthService, instanceService: InstanceService, modalController: ModalController, layoutService: LayoutService) {
    super(dataService, authService, instanceService, modalController, layoutService);
  }

  ngOnInit() {
    if (this.externalLogoUrl) {
      this.imageUrl = this.externalLogoUrl;
    } else if (this.iconAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.iconAssetId}/content?height=400`;
    } else {
      this.imageUrl = 'assets/images/no-image.png';
    }
  }
}
