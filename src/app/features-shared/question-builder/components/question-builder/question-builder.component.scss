.question-container {
  border: 1px solid #4e4e4e;
  border-radius: 5px;
  margin-top: 0.5em;
  color: white;
  font-size: 16px;
  --padding-start: 8px !important;
  caret-color: #7f550c;
  background-color: var(--background-color);
  min-height: 100%;

  ion-select {
    width: 150px;
    background-color: #292929;
    margin: 16px 0px 100px 16px;
    border-radius: 5px;
  }

  .inner-container {
    margin: 10px 16px 0px 16px;
    ion-label {
      color: white;
      font-size: 16px;
    }

    .quill-container {
      border-radius: 5px;
      border: 1px solid rgb(78, 78, 78);
    }
  }
}

.question-grid {
  ion-input {
    border-radius: 0px 3px 3px 3px;
    --padding-start: 8px !important;
    caret-color: #7f550c;
    background-color: var(--background-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 20px 11px 9px 20px;
    box-shadow: 1px 1px 14px rgba(249, 158, 0, 0.15);
    background: #222;
    color: #aaaaaa;
    font-family: 'Roboto';
    font-weight: 400;
    font-size: 16px;
    line-height: 1.3;
    letter-spacing: 0.5px;
    text-align: left;
  }
}

.onlyQuestion {
  .quill-container {
    border-radius: 5px;
    border: 1px solid rgb(78, 78, 78);
    background-color: var(--background-color);
    margin-right: 20px;
    margin-left: 20px;
  }
}

ion-row {
  margin-top: 16px;
}

.left-margin {
  margin-left: 16px;
}

.right-padding {
  padding-right: 16px;
}

ion-item {
  --border-color: transparent; // default underline color
  --color: white;
  font-size: 20px;
  font-family: 'Exo 2';
  --background: var(--background);
}

.full-width {
  width: 100%;
}

.italic {
  font-style: italic;
}

.answer-text-label {
  color: white;
  margin-left: 20px;
}
