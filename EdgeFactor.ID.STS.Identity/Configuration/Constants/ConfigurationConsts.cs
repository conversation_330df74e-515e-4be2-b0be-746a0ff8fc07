﻿namespace EdgeFactor.ID.STS.Identity.Configuration.Constants
{
    public class ConfigurationConsts
    {
        public const string AdminConnectionStringKey = "AdminConnection";

        public const string ConfigurationDbConnectionStringKey = "ConfigurationDbConnection";

        public const string PersistedGrantDbConnectionStringKey = "PersistedGrantDbConnection";

        public const string IdentityDbConnectionStringKey = "IdentityDbConnection";

        public const string DataProtectionDbConnectionStringKey = "DataProtectionDbConnection";

        public const string ResourcesPath = "Resources";

        public const string AdminConfigurationKey = "AdminConfiguration";

        public const string RegisterConfigurationKey = "RegisterConfiguration";

        public const string AdvancedConfigurationKey = "AdvancedConfiguration";

        public const string CspTrustedDomainsKey = "CspTrustedDomains";
    }
}







