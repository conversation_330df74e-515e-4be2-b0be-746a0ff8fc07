<div class="container">
  <div class="half-width">
    <ion-grid>
      <ion-row class="space-between-row">
        @for (scale of scaleArray; track scale) {
          <ion-col class="max-size-col" (click)="scaleClicked(scale)">
            <div
              class="bottom-container center-content scale-font"
              [ngClass]="{ 'ion-text-center green': scaleSelected(scale), scale: !scaleSelected(scale), 'triangle-top-right': scaleSelected(scale) }">
              @if (scaleSelected(scale)) {
                <mat-icon class="corner-checkbox">check</mat-icon>
              }
              <mat-icon>{{ scale?.icon }}</mat-icon>
            </div>
          </ion-col>
        }
      </ion-row>
      <ion-row class="space-between-row">
        <ion-col class="max-size-col">
          <div class="bottom-label-container">
            <ion-label>Strongly Dislike</ion-label>
          </div>
        </ion-col>
        <ion-col class="max-size-col">
          <div class="bottom-label-container">
            <ion-label>Dislike</ion-label>
          </div>
        </ion-col>
        <ion-col class="max-size-col">
          <div class="bottom-label-container">
            <ion-label>Unsure</ion-label>
          </div>
        </ion-col>
        <ion-col class="max-size-col">
          <div class="bottom-label-container">
            <ion-label>Like</ion-label>
          </div>
        </ion-col>
        <ion-col class="max-size-col">
          <div class="bottom-label-container">
            <ion-label>Strongly Like</ion-label>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>
</div>
