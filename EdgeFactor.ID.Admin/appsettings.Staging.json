{"ConnectionStrings": {"ConfigurationDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "PersistedGrantDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "IdentityDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "AdminLogDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "AdminAuditLogDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "DataProtectionDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True"}, "SeedConfiguration": {"ApplySeed": false}, "DatabaseMigrationsConfiguration": {"ApplyDatabaseMigrations": false}, "DatabaseProviderConfiguration": {"ProviderType": "SqlServer"}, "AdminConfiguration": {"PageTitle": "EdgeFactor.ID", "FaviconUri": "~/favicon.ico", "IdentityAdminRedirectUri": "https://edgefactoridadmin.cbos.co.za/signin-oidc", "IdentityServerBaseUrl": "https://edgefactorid.cbos.co.za", "IdentityAdminCookieName": "IdentityServerAdmin", "IdentityAdminCookieExpiresUtcHours": 12, "RequireHttpsMetadata": false, "TokenValidationClaimName": "name", "TokenValidationClaimRole": "role", "ClientId": "EFAdmin", "ClientSecret": "KM8d#W?EMpt7?j5", "OidcResponseType": "code", "Scopes": ["openid", "profile", "email", "roles"], "AdministrationRole": "Admin", "HideUIForMSSqlErrorLogging": false}, "SecurityConfiguration": {"CspTrustedDomains": ["fonts.googleapis.com", "fonts.gstatic.com", "www.gravatar.com"]}, "SmtpConfiguration": {"Host": "", "Login": "", "Password": ""}, "SendGridConfiguration": {"ApiKey": "", "SourceEmail": "", "SourceName": ""}, "AuditLoggingConfiguration": {"Source": "IdentityServer.Admin.Web", "SubjectIdentifierClaim": "sub", "SubjectNameClaim": "name", "IncludeFormVariables": false}, "CultureConfiguration": {"Cultures": [], "DefaultCulture": null}, "HttpConfiguration": {"BasePath": ""}, "IdentityOptions": {"Password": {"RequiredLength": 8}, "User": {"RequireUniqueEmail": true}, "SignIn": {"RequireConfirmedAccount": false}}, "DataProtectionConfiguration": {"ProtectKeysWithAzureKeyVault": false}, "AzureKeyVaultConfiguration": {"AzureKeyVaultEndpoint": "", "ClientId": "", "ClientSecret": "", "TenantId": "", "UseClientCredentials": true, "DataProtectionKeyIdentifier": "", "ReadConfigurationFromKeyVault": false}}