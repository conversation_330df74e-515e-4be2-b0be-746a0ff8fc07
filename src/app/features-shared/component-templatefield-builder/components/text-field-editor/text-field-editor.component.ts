import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-text-field-editor',
    templateUrl: './text-field-editor.component.html',
    styleUrls: ['./text-field-editor.component.scss'],
    standalone: false
})
export class TextFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  textFieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(private formBuilder: UntypedFormBuilder) {}

  get isInheritControl(): AbstractControl {
    return this.textFieldForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.textFieldForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      placeholder: [this.component?.templateField?.placeHolderText, Validators.required],
      helpTitle: [this.component?.templateField?.helpTitle],
      helpDescription: [this.component?.templateField?.helpDescription],
      rowNumber: [this.component?.builderRowNumber],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      isInherit: [this.component?.templateField?.isInherit ?? false],
      isVariable: [this.component.templateField?.isVariable ?? false],
      systemProperty: [this.component?.templateField?.systemProperty?.id],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      defaultText: [this.component?.templateField?.defaultText],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.textFieldForm) {
      return;
    }

    this.textFieldForm.controls.label.setValue(this.component.templateField.label);
    this.textFieldForm.controls.placeholder.setValue(this.component.templateField.placeHolderText);
    this.textFieldForm.controls.helpTitle.setValue(this.component.templateField.helpTitle);
    this.textFieldForm.controls.helpDescription.setValue(this.component.templateField.helpDescription);
    this.textFieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.textFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.textFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.textFieldForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.textFieldForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.textFieldForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.textFieldForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.textFieldForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.textFieldForm.controls.isInherit.setValue(this.component.templateField.isInherit);
    this.textFieldForm.controls.systemProperty.setValue(this.component.templateField.systemProperty);
    this.textFieldForm.controls.isVariable.setValue(this.component.templateField.isVariable);
    this.textFieldForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.textFieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.textFieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.textFieldForm.controls.defaultText.setValue(this.component?.templateField?.defaultText);
    this.textFieldForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.textFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.textFieldForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.textFieldForm.valid) {
      this.component.templateField.label = this.textFieldForm.controls.label.value;
      this.component.templateField.placeHolderText = this.textFieldForm.controls.placeholder.value;
      this.component.templateField.helpTitle = this.textFieldForm.controls.helpTitle.value;
      this.component.templateField.helpDescription = this.textFieldForm.controls.helpDescription.value;
      this.component.builderRowNumber = this.textFieldForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.textFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.textFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.textFieldForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.textFieldForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.textFieldForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.textFieldForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.textFieldForm.controls.isViewField.value;
      this.component.templateField.isInherit = this.textFieldForm.controls.isInherit.value;
      this.component.templateField.isVariable = this.textFieldForm.controls.isVariable.value;
      this.component.templateField.isVisibleRepository = this.textFieldForm.controls.isVisibleRepository.value;
      this.component.templateField.colspan = this.textFieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.textFieldForm.controls.colNumber.value;
      this.component.templateField.defaultText = this.textFieldForm.controls.defaultText.value;
      this.component.templateField.useMaxWidth = this.textFieldForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
