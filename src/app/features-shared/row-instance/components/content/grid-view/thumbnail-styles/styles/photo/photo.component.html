<div
  class="layout"
  (click)="route()"
  [ngClass]="status && (status === 'InProgress' ? 'status-inprogress' : status === 'Completed' ? 'status-complete' : null)"
  [style]="'--background-image:url(' + imageUrl + ');'">
  <ion-row>
    <app-thumbnail-icons
      [row]="row"
      [content]="content"
      [instance]="instance"
      [readingMode]="readingMode"
      [isDraggable]="isDraggable"
      [isEducator]="isEducator"
      [hasAdminAccess]="hasAdminAccess"
      [hideAddButton]="hideAddButton"
      [canHover]="false"
      [isAssignmentRow]="isAssignmentRow"
      (contentRemoved)="emitContentRemoved()"
      (editCustomRowContent)="editContent()">
    </app-thumbnail-icons>
  </ion-row>
  <h1>{{ tagName }}</h1>
</div>
@if (status) {
  <div class="status-foot-container">
    @if (status === 'InProgress') {
      <span class="inner" style="background-color: orange"> IN PROGRESS </span>
    }
    @if (status === 'Completed') {
      <span class="inner" style="background-color: green"> COMPLETED </span>
    }
  </div>
}
