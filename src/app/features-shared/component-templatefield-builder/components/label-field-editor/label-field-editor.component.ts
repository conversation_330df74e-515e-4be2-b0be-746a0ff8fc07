import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-label-field-editor',
    templateUrl: './label-field-editor.component.html',
    styleUrls: ['./label-field-editor.component.scss'],
    standalone: false
})
export class LabelFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  labelFieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(private formBuilder: UntypedFormBuilder) {}

  get isInheritControl(): AbstractControl {
    return this.labelFieldForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.labelFieldForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      placeholder: [this.component?.templateField?.placeHolderText, Validators.required],
      helpTitle: [this.component?.templateField?.helpTitle],
      helpDescription: [this.component?.templateField?.helpDescription],
      rowNumber: [this.component?.builderRowNumber],
      hoverSortOrder: [this.component?.hoverSortOrder],
      instanceSortOrder: [this.component?.instanceSortOrder],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      defaultText: [this.component?.templateField?.defaultText],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.labelFieldForm) {
      return;
    }

    this.labelFieldForm.controls.label.setValue(this.component.templateField.label);
    this.labelFieldForm.controls.placeholder.setValue(this.component.templateField.placeHolderText);
    this.labelFieldForm.controls.helpTitle.setValue(this.component.templateField.helpTitle);
    this.labelFieldForm.controls.helpDescription.setValue(this.component.templateField.helpDescription);
    this.labelFieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.labelFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.labelFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.labelFieldForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.labelFieldForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.labelFieldForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.labelFieldForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.labelFieldForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.labelFieldForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.labelFieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.labelFieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.labelFieldForm.controls.defaultText.setValue(this.component?.templateField?.defaultText);
    this.labelFieldForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.labelFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.labelFieldForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.labelFieldForm.valid) {
      this.component.templateField.label = this.labelFieldForm.controls.label.value;
      this.component.templateField.placeHolderText = this.labelFieldForm.controls.placeholder.value;
      this.component.templateField.helpTitle = this.labelFieldForm.controls.helpTitle.value;
      this.component.templateField.helpDescription = this.labelFieldForm.controls.helpDescription.value;
      this.component.builderRowNumber = this.labelFieldForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.labelFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.labelFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.labelFieldForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.labelFieldForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.labelFieldForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.labelFieldForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.labelFieldForm.controls.isViewField.value;
      this.component.templateField.isVisibleRepository = this.labelFieldForm.controls.isVisibleRepository.value;
      this.component.templateField.colspan = this.labelFieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.labelFieldForm.controls.colNumber.value;
      this.component.templateField.defaultText = this.labelFieldForm.controls.defaultText.value;
      this.component.templateField.useMaxWidth = this.labelFieldForm.controls.useMaxWidth.value;
    }
  }

  setDefaultValue(quillData: any) {
    this.labelFieldForm.controls.defaultText.setValue(quillData);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
