.player-row-flex {
  display: flex;
  flex-direction: row;
  height: 100%;

  .player-width {
    width: 100%;
    position: relative;

    .player-inner-container {
      width: 100%;
      float: left;
      height: 100%;

      .top-border {
        border-color: #171717;
        border-width: 1px;
        border-style: solid;
        width: 100%;
      }

      .content-wrapper {
        height: 100%;
      }

      .player-view-info-container {
        width: 100%;
        overflow-y: auto;
        overflow-anchor: none;
        /* Prevents browser from adjusting scroll position when content changes */
        height: calc(100vh - 175px);
        overscroll-behavior: none;
        /* Prevents scroll chaining */
      }

      .toast-visible {
        padding-bottom: 80px;
      }

      // This should not get changed... If it get's changed the educator view gets affected.
      .custom-info-height {
        height: calc(100vh - 240px) !important;
      }

      .custorm-full-height {
        height: calc(100vh - 160px) !important;
      }

      .searchBar {
        padding-bottom: 80px;
      }

      .searchBar.toast-visible {
        padding-bottom: 160px;
      }

      .player-outer-container {
        margin: auto;
        padding-left: max(60px, 3.5vw);
        padding-right: max(25px, 2.5vw);
        height: 100%;
        overflow: hidden;
        position: relative;
        /* Ensures position is maintained */

        .player-border {
          background-color: #232323;
          border-radius: 13px 13px 15px 15px;
          overflow: hidden;
        }

        .player-container {
          border: 2px solid rgba(249, 158, 0, 0.4);
        }

        .player-container-completed {
          border: 2px solid green;
          // margin-top: 10px;
        }

        .player-container-in-progress {
          border: 2px solid #606060;
          // margin-top: 10px;
        }

        .player-container-incomplete {
          border: 2px solid #5f2524;
          // margin-top: 10px;
        }
      }

      @media screen and (min-width: 993px) and (max-width: 1250px) {
        .player-outer-container {
          padding-left: 30px;
          padding-right: 25px;
        }
      }
    }
  }

  .completed-header-container {
    ion-row {
      z-index: 1006;
      position: absolute;
      top: 0px;
      left: calc(50% - 36px);

      .inner-completed {
        background-color: green;
        padding: 2px;
        font-size: 12px;
        border-radius: 5px;
        color: white;
        margin-right: 16px;
      }

      .inner-in-progress {
        background-color: #ac7f00;
        padding: 2px;
        font-size: 12px;
        border-radius: 5px;
        color: white;
        margin-right: 16px;
      }

      .inner-incomplete {
        background-color: red;
        padding: 2px;
        font-size: 12px;
        border-radius: 5px;
        color: white;
        margin-right: 16px;
      }
    }
  }

  .player-side-panel-container {
    overflow-y: auto;
    height: calc(100vh - 150px);
    width: clamp(425px, 25%, 575px);
    background-color: #292929;
  }

  @media screen and (min-width: 993px) and (max-width: 1250px) {
    .player-side-panel-container {
      width: clamp(400px, 25%, 575px);
    }
  }
}