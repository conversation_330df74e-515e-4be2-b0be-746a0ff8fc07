import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { IFeature, IInstance, IInstanceIn, IKeyValue, IRow, IRowAchievement, IRowAchievementIn } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RolesService } from '@app/core/services/roles.service';
import { OptionSelectorDialogComponent } from '@app/standalone/modals/option-selector-dialog/option-selector-dialog.component';
import { PopoverController } from '@ionic/angular';
import { OverlayEventDetail } from '@ionic/core';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-row-achievement',
    templateUrl: './achievement.component.html',
    styleUrls: ['./achievement.component.scss'],
    standalone: false
})
export class RowAchievementComponent implements OnInit, OnD<PERSON>roy {
  @Input() row: IRow;
  @Input() instance: IInstance;
  @Input() componentId: string;
  controlBackground = 'none';
  achievementTypes: IKeyValue<string, string>[] = [
    { key: 'new', value: 'New Achievement' },
    { key: 'existing', value: 'Existing Achievement' },
  ];
  achievements: IRowAchievement[];
  achievementFeatureBase: IFeature;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private popOver: PopoverController,
    private dataService: DataService,
    private rolesService: RolesService,
    private instanceService: InstanceService
  ) {}

  ngOnInit(): void {
    this.getRowInstanceAchievements();
    this.getAchievementFeatureBase();
  }

  hasRoleAccess() {
    return this.rolesService.hasFeatureRoleAccess([ActionTypes.Manage, ActionTypes.Publish]);
  }

  getAchievementFeatureBase() {
    this.dataService.getFeatureByTypeName('Achievement Manager').subscribe(instance => {
      this.achievementFeatureBase = instance;
    });
  }

  getRowInstanceAchievements() {
    this.dataService.getRowInstanceAchievements(this.row.id, this.instance.id).subscribe(achievments => {
      if (achievments) {
        this.achievements = achievments;
      }
    });
  }

  async openSelectAchievementType(event: any) {
    const popover = await this.popOver.create({
      component: OptionSelectorDialogComponent,
      cssClass: 'question-type-popover',
      componentProps: {
        options: this.achievementTypes,
      },
      event: event,
      side: 'bottom',
    });

    popover.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
      if (overlayEventDetail?.data?.key === 'new') {
        this.createNewAchievement();
      } else if (overlayEventDetail?.data?.key === 'existing') {
        this.openSelectExistingAchievement(event);
      }
    });

    await popover.present();
  }

  createNewAchievement() {
    this.dataService
      .createInstance(this.populateNewInstanceIn())
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.dataService
          .createRowAchievement({ id: null, rowId: this.row.id, achievementInstanceId: data.id, instanceId: this.instance.id } as IRowAchievementIn)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(() => {
            this.instanceService.openInstance('achievement', data.id);
          });
      });
  }

  async openSelectExistingAchievement(event: any) {
    this.dataService.getComponentAchievements(this.componentId).subscribe(async data => {
      if (data) {
        const popover = await this.popOver.create({
          component: OptionSelectorDialogComponent,
          cssClass: 'question-type-popover',
          componentProps: {
            options: data.map(x => ({ key: x.achievementInstance.id, value: x.achievementInstance.title }) as IKeyValue<string, string>),
          },
          event: event,
          side: 'bottom',
        });

        popover.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
          if (overlayEventDetail?.data) {
            this.dataService
              .createRowAchievement({ id: null, rowId: this.row.id, achievementInstanceId: overlayEventDetail.data.key, instanceId: this.instance.id } as IRowAchievementIn)
              .pipe(takeUntil(this.componentDestroyed$))
              .subscribe(() => {
                this.getRowInstanceAchievements();
              });
          }
        });

        await popover.present();
      }
    });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }

  private populateNewInstanceIn() {
    const newInstance: IInstanceIn = {
      title: this.achievementFeatureBase.title,
      description: this.achievementFeatureBase.description,
      featureId: this.achievementFeatureBase.id,
      organizationId: this.instance.organizationId,
      isDefault: true,
    };
    return newInstance;
  }
}
