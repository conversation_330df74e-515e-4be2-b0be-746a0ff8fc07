import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { IInstanceSection, IInstanceSectionIn, ISidePanelBuilder } from '@app/core/contracts/contract';
import { SidePanelBuilderType } from '@app/core/enums/builder-selection-types';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, debounceTime, takeUntil } from 'rxjs';

@Component({
    selector: 'app-instance-section-edit',
    templateUrl: './instance-section-edit.component.html',
    styleUrls: ['./instance-section-edit.component.scss'],
    standalone: false
})
export class InstanceSectionEditComponent implements OnInit, OnChanges, On<PERSON><PERSON>roy {
  @Input() panelBuilder?: ISidePanelBuilder;
  @Input() instanceId: string;
  @Input() templateId: string;
  @Input() instanceSection: IInstanceSection | null;
  @Output() instanceSectionUpdated = new EventEmitter<string>();
  sectionForm: UntypedFormGroup;
  builderType = SidePanelBuilderType;
  formValueChanges$: Subscription;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private builderService: BuilderService
  ) {}

  ngOnInit() {
    this.createSectionForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['section']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createSectionForm() {
    this.sectionForm = new UntypedFormGroup({
      title: new UntypedFormControl(this.instanceSection?.title, [Validators.required]),
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.sectionForm) {
      return;
    }

    this.sectionForm.controls.title.setValue(this.instanceSection?.title);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.sectionForm.valueChanges.pipe(debounceTime(1000), takeUntil(this.componentDestroyed$)).subscribe(() => {
      const sectionIn = {
        ...this.instanceSection,
        title: this.sectionForm.controls.title.value,
        instanceId: this.instanceId,
        templateId: this.templateId,
        masterSectionId: null,
      } as IInstanceSectionIn;

      if (this.sectionForm.valid) {
        this.updateInstanceSection(sectionIn);
      }
    });
  }

  updateInstanceSection(section: IInstanceSectionIn) {
    this.dataService
      .updateInstanceSections([section])
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.instanceSectionUpdated.next(section.title);
      });
  }

  back() {
    this.builderService.selectedSection$.next(null);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
