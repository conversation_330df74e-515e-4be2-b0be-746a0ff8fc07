import { Component, Input, OnD<PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { IInstance } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { environment } from '@env/environment';
import { PopoverController } from '@ionic/angular';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

@Component({
    selector: 'app-row-add-content',
    templateUrl: './add-content.component.html',
    styleUrls: ['./add-content.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class AddRowContentComponent implements OnInit, OnDestroy {
  componentDestroyed$: Subject<boolean> = new Subject();
  searchForm: UntypedFormGroup;
  instanceSearch: UntypedFormControl;
  instances: IInstance[];
  rowId: string;
  organizationId: string;
  currentEnvironment: string;
  processing: boolean;

  constructor(
    private popoverController: PopoverController,
    private dataService: DataService
  ) {}

  @Input() onClose = () => {};

  ngOnInit() {
    this.currentEnvironment = environment.contentUrl;
    this.createFormControls();
    this.createForm();
    this.searchInstances();
  }

  createFormControls() {
    this.instanceSearch = new FormControl('');
  }

  createForm() {
    this.searchForm = new FormGroup({
      instanceSearch: this.instanceSearch,
    });

    this.searchForm.controls.instanceSearch.valueChanges.pipe(debounceTime(700), distinctUntilChanged()).subscribe(() => {
      this.searchInstances();
    });
  }

  searchInstances() {
    this.instances = [];
    const searchValue = encodeURIComponent(this.instanceSearch.value);
    this.dataService
      .searchInstances(this.organizationId, searchValue)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((res: IInstance[]) => {
        this.instances = res;
      });
  }

  addRowContent(instanceId: string) {
    if (!this.processing) {
      this.processing = true;
      this.dataService
        .addRowContent(this.rowId, instanceId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((contentAdded: boolean) => {
          if (contentAdded) {
            this.processing = false;
            this.close();
          }
        });
    }
  }

  close() {
    this.popoverController.dismiss();
    this.onClose();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
