import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { IComponent, IInstanceSectionComponent, ISection, ISidePanelBuilder } from '@app/core/contracts/contract';
import { SectionTypes } from '@app/core/enums/section-types.enum';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { environment } from '@env/environment';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-dynamic-section-components',
    templateUrl: './dynamic-section-components.component.html',
    styleUrls: ['./dynamic-section-components.component.scss'],
    standalone: false
})
export class DynamicSectionComponentsComponent implements OnInit, OnChanges, OnDestroy {
  @Input() section: ISection;
  @Input() panelBuilder: ISidePanelBuilder;
  @Output() sectionComponentsChanged = new EventEmitter<IInstanceSectionComponent[]>();
  sectionTypes = SectionTypes;
  dynamicComponentList: IComponent[];
  selectedComponentIds: string[] = [];
  query: string;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private builderService: BuilderService
  ) {}

  ngOnInit() {
    this.getDynamicComponentList(this.section.id);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['section']) {
      this.getDynamicComponentList(this.section.id);
    }
  }

  getDynamicComponentList(sectionId: string) {
    this.dataService
      .getDynamicSectionComponentTypes(sectionId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(res => {
        if (res) {
          this.dynamicComponentList = res;
        }
      });
  }

  setImage(assetId: any) {
    return `${environment.contentUrl}asset/${assetId}/content`;
  }

  isSelected(componentId: string) {
    return this.selectedComponentIds ? this.selectedComponentIds.findIndex(x => x === componentId) !== -1 : false;
  }

  checkboxChanged(componentId: string) {
    const index = this.selectedComponentIds.findIndex(x => x === componentId);
    if (index === -1) {
      this.selectedComponentIds.push(componentId);
    } else {
      this.selectedComponentIds.splice(index, 1);
    }
  }

  back() {
    this.builderService.selectedSection$.next(null);
  }

  getDynamicComponentListFiltered() {
    return this.query && this.query.length > 0 ? this.dynamicComponentList.filter(x => x.componentType.name.toLowerCase().includes(this.query.toLowerCase())) : this.dynamicComponentList;
  }

  save() {
    this.dataService
      .addInstanceSectionComponents(this.panelBuilder.id, this.selectedComponentIds)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((sectionComponents: IInstanceSectionComponent[]) => {
        if (sectionComponents) {
          this.sectionComponentsChanged.emit(sectionComponents);
        }
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
