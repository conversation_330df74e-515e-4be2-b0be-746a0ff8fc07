import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { IQuestion, IUserAnswer } from '@app/core/contracts/contract';
import { Subject, debounceTime } from 'rxjs';

@Component({
    selector: 'app-text-question',
    templateUrl: './text-question.component.html',
    styleUrls: ['./text-question.component.scss'],
    standalone: false
})
export class TextQuestionComponent implements OnChanges {
  @Input() backgroundColor = '#181818';
  @Input() question: IQuestion;
  @Input() userAnswer: IUserAnswer;
  @Input() isShort = false;
  @Input() isKnowledgeQuestion = false;
  @Output() userAnswerChanged = new EventEmitter<IUserAnswer>();
  @Output() shortAnswerInputChanged = new EventEmitter<IUserAnswer>();
  scaleArray: number[] = [];
  debouncer: Subject<IUserAnswer> = new Subject<IUserAnswer>();
  value: string;

  get placeholder() {
    return this.question.questionType.name === 'Short Answer' ? 'Type your answer here...' : '';
  }

  constructor() {
    // you listen to values here which are debounced
    // on every value, you call the outer component
    this.debouncer.pipe(debounceTime(1500)).subscribe(value => this.userAnswerChanged.emit(value));
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['question']) {
      this.value = this.question?.userAnswer?.answerText ?? '';
    }
  }

  saveText(answer: any) {
    if (!this.userAnswer) {
      this.userAnswer = {
        id: '',
        instanceId: '',
        userId: '',
        answerText: answer.target.value,
        userQuestionAnswers: [],
        questionId: this.question.id,
        instanceComponentId: null,
        marks: null,
        gradePercentage: null,
        isSubmitted: null,
        isAutoGraded: false,
        isEducatorGraded: false,
        answerStatusTypeBw: null,
        feedback: null,
      } as IUserAnswer;
    } else {
      this.userAnswer.answerText = answer.target.value;
    }

    this.shortAnswerInputChanged.emit(this.userAnswer);

    this.debouncer.next(this.userAnswer);
  }
}
