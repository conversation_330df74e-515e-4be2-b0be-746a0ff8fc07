ion-item {
  border: 1px solid #4e4e4e;
  border-radius: 5px;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: white;
  font-size: 16px;
  --padding-start: 8px !important;
  caret-color: #7f550c;
  background-color: var(--background-color);
}

.input {
  border: 1px solid #4e4e4e;
  border-radius: 5px;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: white;
  font-size: 16px;
  --padding-start: 8px !important;
  caret-color: #7f550c;
  background-color: var(--background-color);
}

.scale-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  justify-content: space-between;

  ion-label {
    height: fit-content;
    margin-left: 5px;
    margin-right: 5px;
  }
}

.top-label {
  margin-bottom: 0.2em;
  margin-left: 20px;
  color: white;
}

.label {
  color: white;
}

.container {
  margin-top: 20px;
  margin-left: 20px;
}
