:host {
  .small-header {
    height: 100px;
    width: 100%;

    .image-header-gradient {
      background-image: linear-gradient(to bottom, rgba(30, 30, 30, 0.35) 25%, rgb(30, 30, 30) 75%), var(--background-image);
      background-size: cover;
      height: 100px;
      border-right: 3px solid #292929;
      border-bottom: 2px solid #454545;

      display: flex;
      align-items: flex-end;
      width: 100%;

      .inner-container {
        display: flex;
        align-items: flex-end;
        width: 100%;

        .back-button-col {
          width: 75px;
          max-width: 75px;
          padding-left: 20px;
          padding-right: 15px;

          .back-btn {
            margin-bottom: 15px;
            font-weight: 500;
            font-size: 18px;
            line-height: 1;

            ion-icon {
              width: 24px;
              height: 24px;
            }
          }
        }

        .header-col {
          .title-container {
            margin-bottom: 10px;
            .title {
              padding-left: 15px;
              color: #fff;
              font-weight: 900;
              font-family: 'Exo 2';
              font-size: 1.875em;
              line-height: 1.1;
              letter-spacing: 0.03em;

              ion-icon[name='information-circle-outline'] {
                display: inline-block !important;
                width: 22px;
                height: 22px;
                font-size: 22px;
                padding-left: 9px;
                color: #f99e00;
              }
            }
          }

          .description-container {
            .description {
              color: #aaaaaa;
              font-family: 'Roboto';
              font-weight: 400;
              font-size: 0.875em;
              font-style: italic;
              letter-spacing: 0.03em;
              line-height: 1.1;
              padding-bottom: 0px;
              padding-left: 15px;
            }
          }
        }
      }
    }
  }

  .with-header {
    height: calc(100% - 250px);
  }

  .no-header {
    height: 100%;
  }

  .side-panel-builder-container {
    height: 100%;
    .side-nav-container {
      display: flex;
      flex-direction: row;
      height: 100%;
      background-color: transparent;

      .side-nav-inner-container {
        background-color: #2d2e32;
      }

      @media (min-width: 956px) {
        .preview-parent-container-height {
          height: calc(100vh - 150px);
        }

        .preview-parent-container-height-with-header {
          height: calc(100vh - 270px);
        }
      }

      @media (min-width: 1300px) {
        .preview-parent-container-height-with-header {
          height: calc(100vh - 290px);
        }
      }

      @media (min-width: 1700px) {
        .preview-parent-container-height-with-header {
          height: calc(100vh - 300px);
        }
      }

      @media (max-width: 956px) {
        .preview-parent-container-height {
          height: calc(100vh - 200px);
        }

        .preview-parent-container-height-with-header {
          height: calc(100vh - 310px);
        }
      }

      .preview-parent-content-container {
        // overwrite inline styles
        --offset-bottom: auto !important;
        --overflow: hidden;
        overflow: auto;

        &::-webkit-scrollbar {
          display: none;
        }
      }

      .builder-content {
        max-width: 1250px;
        margin: 0 auto;
        padding-left: 2.5vw;
        padding-right: 1.5vw;
        padding-top: 15px;
        overflow: hidden;

        .builder-preview {
          height: 100%;
          width: 100%;
          float: left;
        }
      }

      .cdk-drag-handle {
        position: absolute;
        left: 0px;
        top: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .svg-icon {
          border-top-right-radius: 5px;
          border-bottom-right-radius: 5px;
          padding: 15px 5px 15px 0px;
          background-color: #2d2e32;
          font-size: 25px;
          color: gray;
          cursor: ew-resize;
        }
      }
    }
  }

  ::ng-deep .mat-drawer-inner-container {
    overflow-y: hidden;
  }
}
