import { CdkDrag, CdkDropList } from '@angular/cdk/drag-drop';
import { NgModule } from '@angular/core';
import { SharedModule } from '@app/shared/shared.module';
import { featureComponents, standaloneComponents } from './question-active.declarations';

@NgModule({
  declarations: [...featureComponents],
  imports: [...standaloneComponents, SharedModule, CdkDropList, CdkDrag],
  exports: [...featureComponents],
})
export class QuestionActiveModule {}
