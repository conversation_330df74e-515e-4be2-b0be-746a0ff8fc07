import { NgModule } from '@angular/core';
import { RowInstanceModule } from '@app/features-shared/row-instance/row-instance.module';
import { RowManagerModule } from '../row-manager/row-manager.module';
import { featureComponents, standaloneComponents } from './component-templatefield-builder.declarations';
import { QuestionModule } from '../question/question.module';
import { SharedModule } from '@app/shared/shared.module';
import {MatProgressSpinner} from "@angular/material/progress-spinner";

@NgModule({
  declarations: [...featureComponents],
    imports: [...standaloneComponents, SharedModule, RowInstanceModule, QuestionModule, RowManagerModule, MatProgressSpinner],
  exports: [...featureComponents, RowInstanceModule, QuestionModule],
})
export class ComponentTemplatefieldBuilderModule {}
