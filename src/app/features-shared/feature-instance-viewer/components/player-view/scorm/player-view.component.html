<div class="player-row-flex">
  <div class="player-width">
    <div class="player-inner-container">
      <div class="top-border" slot="fixed">
        <app-view-options-row
          [template]="template"
          [canAdd]="instance?.title === 'Admin'"
          (buttonChanged)="buttonClicked($event)"
          [featureTab]="featureTab"
          (optionSelected)="setSelectedViewType($event)"
          (rowFiltered)="rowFilteredChange($event)"
          [selectedItem]="routeParams?.viewType"
          (mobileMenuClicked)="openMobileMenu = !openMobileMenu"></app-view-options-row>
      </div>
      <div class="content-wrapper player-view-info-container">
        <div [class.player-outer-container]="routeParams?.viewType === viewTypes.Player">
          <div [class.player-container]="routeParams?.viewType === viewTypes.Player">
            <app-player-view-information [routeParams]="routeParams" [parentInstance]="instance" (finishedInstance)="setFinishedInstance($event)"></app-player-view-information>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- <div class="player-side-panel-container">
  <app-player-view-side-panel
    [template]="template"
    [selectedId]="selectedId"
    [rowViewType]="rowViewType"
    [id]="id"
    [instance]="instance"
    (selectedIdChanged)="setSelected($event)"></app-player-view-side-panel>
  </div> -->
</div>
