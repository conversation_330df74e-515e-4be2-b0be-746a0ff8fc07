<ng-container [formGroup]="fieldForm">
  <ion-row>
    <ion-col>
      <app-text-input-control [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Add field title...'" formControlName="label"></app-text-input-control>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <app-text-input-control [backgroundColor]="'#292929'" [label]="'Placeholder Text'" [placeHolder]="'Add field placeholder...'" formControlName="placeholder"></app-text-input-control>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <app-text-input-control [backgroundColor]="'#292929'" [label]="'Help Title'" [placeHolder]="'Add field help title...'" formControlName="helpTitle"></app-text-input-control>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <app-text-input-control [backgroundColor]="'#292929'" [label]="'Help Description'" [placeHolder]="'Add field help description...'" formControlName="helpDescription"></app-text-input-control>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <app-text-input-control [backgroundColor]="'#292929'" [label]="'Row Number'" [placeHolder]="'Add field row number...'" formControlName="rowNumber" [type]="'number'"></app-text-input-control>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <app-text-input-control
        [backgroundColor]="'#292929'"
        [label]="'Column Number (Out of 12)'"
        [limit]="12"
        [placeHolder]="'Add column number (Out of 12)...'"
        formControlName="colNumber"
        [type]="'number'"></app-text-input-control>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <app-text-input-control
        [backgroundColor]="'#292929'"
        [label]="'Colspan (Out of 12)'"
        [limit]="12"
        [placeHolder]="'Add field colspan (Out of 12)...'"
        formControlName="colspan"
        [type]="'number'"></app-text-input-control>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <app-text-input-control
        [backgroundColor]="'#292929'"
        [label]="'Hover Sort Order'"
        [placeHolder]="'Add field hover sort order...'"
        formControlName="hoverSortOrder"
        [type]="'number'"></app-text-input-control>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <app-text-input-control
        [backgroundColor]="'#292929'"
        [label]="'Instance Sort Order'"
        [placeHolder]="'Add field instance sort order...'"
        formControlName="instanceSortOrder"
        [type]="'number'"></app-text-input-control>
    </ion-col>
  </ion-row>
</ng-container>
