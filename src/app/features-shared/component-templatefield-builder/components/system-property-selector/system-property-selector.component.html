<mat-expansion-panel #panel hideToggle="true">
  @if (!systemPropertyControl?.value) {
    <mat-expansion-panel-header>
      <div class="panel-left">
        <div class="description">
          <h2 class="org-header">- Select a property -</h2>
        </div>
      </div>
      <div class="panel-right">
        @if (panel.expanded) {
          <mat-icon svgIcon="caret_up"></mat-icon>
        }
        @if (!panel.expanded) {
          <mat-icon svgIcon="caret_down"></mat-icon>
        }
      </div>
    </mat-expansion-panel-header>
  }
  @if (systemPropertyControl?.value) {
    <mat-expansion-panel-header>
      <div class="panel-left">
        <div class="description">
          <span class="org-links">Links to:</span>
          <div class="row">
            <h2 class="org-header">{{ isParentSystemPropertyLinkField ? templateField?.parentIdSystemPropertyLink?.name : templateField?.systemProperty?.name }}</h2>
            <h2 class="org-title">({{ isParentSystemPropertyLinkField ? templateField?.parentIdSystemPropertyLink?.type?.title : templateField?.systemProperty?.type?.title }})</h2>
          </div>
        </div>
      </div>
      <div class="panel-right">
        @if (panel.expanded) {
          <mat-icon svgIcon="caret_up"></mat-icon>
        }
        @if (!panel.expanded) {
          <span class="underline-text">Edit</span>
        }
      </div>
    </mat-expansion-panel-header>
  }
  <input class="property" matInput placeholder="Search for a property..." [(ngModel)]="searchString" (ngModelChange)="getSystemProperties()" />
  <form [formGroup]="formGroup">
    <ion-radio-group class="properties-container" [formControlName]="isParentSystemPropertyLinkField ? 'parentIdSystemPropertyLink' : 'systemProperty'">
      @for (property of systemProperties; track property) {
        <div class="property" (click)="setPropertySelected(property.id)">
          <div class="property-left">
            <ion-radio class="radio" color="warning" [value]="property.id"></ion-radio>
            <h2 class="propertyName">{{ property?.name }}</h2>
          </div>
          <h4 class="type">
            {{ property.type.title }}
          </h4>
        </div>
      }
    </ion-radio-group>
  </form>
</mat-expansion-panel>
