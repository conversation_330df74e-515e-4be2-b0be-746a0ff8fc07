@if (isDraggable && maxWidth) {
  <div [style]="'margin-right: -11px;'" cdkDropListGroup>
    @for (gridRow of grid; track gridRow) {
      <div cdkDropList [cdkDropListData]="gridRow" (cdkDropListDropped)="dropListDropped($event)" class="row">
        @for (item of gridRow; track item) {
          <div cdkDrag class="item" [style]="'aspect-ratio:' + aspectRatio + ';max-width: calc(' + maxWidth + ' - 11px);'" [ngClass]="{'dummy': item.id === 'dummy'}">
            <div class="placeholder" *cdkDragPlaceholder></div>
            <div style="width: 100%">
              <ng-container [ngTemplateOutlet]="thumbnailTypesTemplate" [ngTemplateOutletContext]="{ $implicit: item }"></ng-container>
            </div>
          </div>
        }
      </div>
    }
  </div>
}

@if (!isDraggable && maxWidth) {
  <div [style]="'margin-right: -11px;'">
    @for (gridRow of grid; track gridRow; let i = $index) {
      <div class="row">
        @if (!rowLimit || incrementNoneEmptyRow(gridRow, i) < rowLimit) {
          @for (item of gridRow; track item) {
            <div class="item" [style]="'aspect-ratio:' + aspectRatio + '; max-width: calc(' + maxWidth + ' - 11px)'" [ngClass]="{'dummy': item.id === 'dummy'}">
              <ng-container [ngTemplateOutlet]="thumbnailTypesTemplate" [ngTemplateOutletContext]="{ $implicit: item }"></ng-container>
            </div>
          }
        }
      </div>
    }
  </div>
}
