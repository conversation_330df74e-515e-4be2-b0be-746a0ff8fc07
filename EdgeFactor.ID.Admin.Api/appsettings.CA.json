{"ConnectionStrings": {"AdminLogDbConnection": "data source=edgefactor.centralus.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=CBOS;password=***********$;MultipleActiveResultSets=true;TrustServerCertificate=True", "AdminAuditLogDbConnection": "data source=edgefactor.centralus.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=CBOS;password=***********$;MultipleActiveResultSets=true;TrustServerCertificate=True", "ConfigurationDbConnection": "data source=edgefactor.centralus.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=CBOS;password=***********$;MultipleActiveResultSets=true;TrustServerCertificate=True", "PersistedGrantDbConnection": "data source=edgefactor.centralus.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=CBOS;password=***********$;MultipleActiveResultSets=true;TrustServerCertificate=True", "IdentityDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_CA;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "DataProtectionDbConnection": "data source=edgefactor.centralus.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=CBOS;password=***********$;MultipleActiveResultSets=true;TrustServerCertificate=True"}, "AdminApiConfiguration": {"ApiName": "EdgeFactor.ID Api", "ApiVersion": "v1", "ApiBaseUrl": "https://idapica.edgefactor.com", "IdentityServerBaseUrl": "https://idca.edgefactor.com", "OidcSwaggerUIClientId": "EFAdmin_api_swaggerui", "OidcApiName": "EFAdmin_api", "AdministrationRole": "Admin", "RequireHttpsMetadata": false, "CorsAllowAnyOrigin": true, "CorsAllowOrigins": []}, "SmtpConfiguration": {"Host": "", "Login": "", "Password": ""}, "SendGridConfiguration": {"ApiKey": "", "SourceEmail": "", "SourceName": ""}, "DatabaseProviderConfiguration": {"ProviderType": "SqlServer"}, "AuditLoggingConfiguration": {"Source": "IdentityServer.Admin.Api", "SubjectIdentifierClaim": "sub", "SubjectNameClaim": "name", "ClientIdClaim": "client_id"}, "IdentityOptions": {"Password": {"RequiredLength": 8}, "User": {"RequireUniqueEmail": true}, "SignIn": {"RequireConfirmedAccount": false}}, "DataProtectionConfiguration": {"ProtectKeysWithAzureKeyVault": false}, "AzureKeyVaultConfiguration": {"AzureKeyVaultEndpoint": "", "ClientId": "", "ClientSecret": "", "TenantId": "", "UseClientCredentials": true, "DataProtectionKeyIdentifier": "", "ReadConfigurationFromKeyVault": false}, "RegisterConfiguration": {"Enabled": true, "IdentitySource": 1, "EFApiBaseUrl": "https://api.edgefactor.com/v1", "EFBaseUrl": "https://app.edgefactor.com", "Environment": {"Location": "Canada", "LocationId": "465EC789-F15D-4F3D-B2C8-E23FAFEBA835"}}, "MailTemplates": {"NewRegistration": "d-cbaabf2b318847eb8d4f56133044b968", "NewAccount": "d-f7e83b62b92244d89fa9a584986883d1", "PasswordReset": "d-b7ef3470b749468aad28bd4073c5a569", "EmailVerification": "d-27b8a11c61a54166980b1928daf3ef89", "UserCredentials": "d-f7e83b62b92244d89fa9a584986883d1"}, "Urls": {"TermsUrl": "https://app.edgefactor.com/terms-of-use", "PrivacyUrl": "https://app.edgefactor.com/privacy-policy", "ContactUrl": "https://offers.edgefactor.com/contact-us", "VerificationUrl": "", "returnUrl": "https://app.edgefactor.com/auth-callback/external", "aboutUrl": "https://offers.edgefactor.com/aboutedgefactor"}}