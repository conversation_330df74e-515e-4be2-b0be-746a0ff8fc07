{"ConnectionStrings": {"AdminLogDbConnection": "data source=edgefactor.centralus.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID_Staging;persist security info=True;user id=CBOS;password=***********$;MultipleActiveResultSets=true;TrustServerCertificate=True", "AdminAuditLogDbConnection": "data source=edgefactor.centralus.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID_Staging;persist security info=True;user id=CBOS;password=***********$;MultipleActiveResultSets=true;TrustServerCertificate=True", "ConfigurationDbConnection": "data source=edgefactor.centralus.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID_Staging;persist security info=True;user id=CBOS;password=***********$;MultipleActiveResultSets=true;TrustServerCertificate=True", "PersistedGrantDbConnection": "data source=edgefactor.centralus.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID_Staging;persist security info=True;user id=CBOS;password=***********$;MultipleActiveResultSets=true;TrustServerCertificate=True", "IdentityDbConnection": "data source=.,1433;initial catalog=EdgeFactor_ID_CA_Staging;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=True", "DataProtectionDbConnection": "data source=edgefactor.centralus.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID_Staging;persist security info=True;user id=CBOS;password=***********$;MultipleActiveResultSets=true;TrustServerCertificate=True"}, "AdminApiConfiguration": {"ApiName": "EdgeFactor.ID Api", "ApiVersion": "v1", "ApiBaseUrl": "https://edgefactoridapica.cbos.co.za", "IdentityServerBaseUrl": "https://edgefactoridca.cbos.co.za", "OidcSwaggerUIClientId": "EFAdmin_api_swaggerui", "OidcApiName": "EFAdmin_api", "AdministrationRole": "Admin", "RequireHttpsMetadata": false, "CorsAllowAnyOrigin": true, "CorsAllowOrigins": []}, "SmtpConfiguration": {"Host": "", "Login": "", "Password": ""}, "SendGridConfiguration": {"ApiKey": "", "SourceEmail": "", "SourceName": ""}, "DatabaseProviderConfiguration": {"ProviderType": "SqlServer"}, "AuditLoggingConfiguration": {"Source": "IdentityServer.Admin.Api", "SubjectIdentifierClaim": "sub", "SubjectNameClaim": "name", "ClientIdClaim": "client_id"}, "IdentityOptions": {"Password": {"RequiredLength": 8}, "User": {"RequireUniqueEmail": true}, "SignIn": {"RequireConfirmedAccount": false}}, "DataProtectionConfiguration": {"ProtectKeysWithAzureKeyVault": false}, "RegisterConfiguration": {"Enabled": true, "IdentitySource": 1, "EFApiBaseUrl": "https://edgefactorapi.cbos.co.za/v1", "EFBaseUrl": "https://edgefactor.cbos.co.za", "Environment": {"Location": "Canada", "LocationId": "465EC789-F15D-4F3D-B2C8-E23FAFEBA835"}}, "AzureKeyVaultConfiguration": {"AzureKeyVaultEndpoint": "", "ClientId": "", "ClientSecret": "", "TenantId": "", "UseClientCredentials": true, "DataProtectionKeyIdentifier": "", "ReadConfigurationFromKeyVault": false}}