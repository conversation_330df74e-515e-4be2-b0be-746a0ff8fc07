import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-empty-default-assignment',
    templateUrl: './empty-default-assignment.component.html',
    styleUrls: ['./empty-default-assignment.component.scss'],
    standalone: false
})
export class EmptyDefaultAssignmentComponent implements OnInit {
  @Input() notAssignment = false;
  @Input() title = 'Add content to this assignment.';
  @Input() description = " Use the 'add to' button while browsing the library or click the 'add content' button above to begin assigning content.";
  imageUrl = 'assets/images/Default_empty_assignments.png';

  ngOnInit(): void {
    if (this.notAssignment === true) {
      this.title = 'There is no content in this row yet.';
      this.description = 'This is where you will add the content.';
    }
  }
}
