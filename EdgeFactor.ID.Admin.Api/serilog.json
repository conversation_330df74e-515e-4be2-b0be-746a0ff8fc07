{"Serilog": {"MinimumLevel": {"Default": "Error", "Override": {"Skoruba": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Log/skoruba_admin.txt", "rollingInterval": "Day"}}, {"Name": "MSSqlServer", "Args": {"connectionString": "server=edgefactor.centralus.cloudapp.azure.com,1433;uid=CBOS;pwd=***********$;database=EdgeFactor_ID", "tableName": "Log", "columnOptionsSection": {"addStandardColumns": ["LogEvent"], "removeStandardColumns": ["Properties"]}}}]}}