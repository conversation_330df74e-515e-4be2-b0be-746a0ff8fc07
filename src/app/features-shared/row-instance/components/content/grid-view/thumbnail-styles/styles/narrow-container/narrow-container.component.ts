import { Component, Input, OnInit } from '@angular/core';
import { IRouteParams } from '@app/core/contracts/contract';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RowContentHoverValues } from '@app/shared/directives/row-content-hover.directive';
import { environment } from '@env/environment';
import { ModalController } from '@ionic/angular';
import { ThumbnailStyleBaseComponent } from '../../base/thumbnail-style-base.component';
import { LayoutService } from '@app/core/services/layout-service';

@Component({
    selector: 'app-row-narrow-container',
    templateUrl: './narrow-container.component.html',
    styleUrls: ['./narrow-container.component.scss'],
    standalone: false
})
export class NarrowContainerComponent extends ThumbnailStyleBaseComponent implements OnInit {
  @Input() alignment: string | undefined;
  @Input() sameUrlNavigation: boolean;
  @Input() isEducator: boolean = false;
  @Input() hasAdminAccess: boolean = false;
  @Input() hideAddButton: boolean = false;
  @Input() isAssignmentRow?: boolean = false;
  noImageUrl = 'assets/images/no-image.png';
  rowContentHoverValues: RowContentHoverValues;
  contentParams: IRouteParams;

  constructor(dataService: DataService, authService: AuthService, instanceService: InstanceService, modalController: ModalController, layoutService: LayoutService) {
    super(dataService, authService, instanceService, modalController, layoutService);
  }

  ngOnInit() {
    this.setAssetUrls();
    this.setCompletionAchievementUrls();
    this.contentParams = { ...this.routeParams, instanceSlug: this.content.slug ?? this.content.id } as IRouteParams;
  }

  setAssetUrls() {
    if (this.instance.coverAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.instance.coverAssetId}/content?height=600`;
    } else if (this.content.coverAssetId) {
      this.imageUrl = `${environment.contentUrl}asset/${this.content.coverAssetId}/content?height=600`;
    } else {
      this.imageUrl = 'assets/images/no-image.png';
    }
  }

  updateRowContentHoverValues(value: any) {
    this.rowContentHoverValues = value;
  }
}
