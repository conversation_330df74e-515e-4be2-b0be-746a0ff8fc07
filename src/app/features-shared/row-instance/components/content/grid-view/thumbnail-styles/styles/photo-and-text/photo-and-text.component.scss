.gradient-overlay {
  justify-content: center;
  text-align: center;
  padding-left: 15px;
  padding-right: 15px;
  background: radial-gradient(rgba(0, 0, 0, .85) 0%, rgba(0, 0, 0, .35) 100%);
  border-radius: 7px;
  width: 100%;
}

.title {
  font-family: 'Roboto';
  font-weight: 900;
  font-size: 1.250em;
  line-height: 1.1;
  letter-spacing: 0.01em;
  color: white;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media screen and (max-width: 960px) {
  .title {
    font-size: 16px;
  }
}

.description {
  font-family: 'Roboto';
  font-weight: 400;
  font-size: 1em;
  line-height: 1.4;
  letter-spacing: 0.01em;
  color: #ccc;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media screen and (max-width: 960px) {
  .description {
    font-size: 10px;
  }
}

.descriptors-container {
  width: 100%;
  margin: 0;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.hover-descriptors-container {
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 60px;
  padding-bottom: 30px;
}

.inner-html {
  font-family: 'Roboto';
  color: #aaa;
  font-size: 0.85em;
  font-weight: 400;
  font-style: italic;
  line-height: 1.1;
  letter-spacing: 0.01em;
  height: auto;
  overflow: auto;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  margin-top: 3px;
  margin-bottom: 3px;
}

.image {
  background-image: var(--background-image);
  background-size: cover;
  width: 100%;
  border-radius: 7px;
  aspect-ratio: 3 / 1.25;
  display: flex
}

.item {
  margin-bottom: 13px
}

.button-container {
  margin-top: 15px;
  padding: 0px;
}

.button-container ion-button {
  --background: #ccc;
  font-weight: 600;
  font-family: 'Roboto';
  font-size: 1em;
  color: black;
  line-height: 1.1em;
  border-radius: 3px;
  letter-spacing: 0.01em;
}

.button-container ion-button:hover {
  --background: #f99e00;
}

.badge-icon {
  position: absolute;
  left: 0px;
  top: -12px;
  z-index: 3;
  width: 70px;
  height: 70px;
  border-radius: 5px;
}

.status-inprogress {
  outline: 2px solid orange;
  border-radius: 5px;
}

.status-complete {
  outline: 2px solid green;
  border-radius: 5px;
}

.add-button-container {
  position: relative;
  display: flex;
  justify-content: flex-end;
}

.right-side {
  position: absolute;
  right: 5px;
  z-index: 3;
}

ion-button {
  --background: #fff;
}

.tracking {
  position: absolute;
  margin: auto;
  margin-top: 3px;
  left: 0px;
  right: 0px;
  width: fit-content;
}

.lock-icon-circle-hover {
  margin-left: 11px;
}

.background-layout {
  background-size: cover !important;
}

.status-foot-container {
  bottom: -3px;
  width: auto;
}

.hover-image {
  border: solid 1.5px #656565;
  position: relative;
  z-index: 2;
  background-image: var(--background-image);
  background-size: cover;
  width: 100%;
  border-radius: 7px;
  display: flex;
  margin-top: -13px;
  flex-direction: column;
}

.hover-item-container {
  margin-top: 15px;
  min-height: 150px;
  height: auto;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: visible;
  max-height: none;
}

@media (min-width: 2200px) {
  .gradient-overlay {
    min-height: 190px;
  }
}

.instance-tracker-container {
  bottom: -13px;
  width: 100%;

  app-instance-tracking {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}

.icon-container {
  display: flex;
  top: 5px;
  position: absolute;
  gap: 5px;
  z-index: 900;
}

.icon-container-right {
  right: 5px;
}

.icon {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-radius: 50%;
  height: 25px;
  width: 25px;
  min-height: 20px;
  min-width: 20px;
  z-index: 5;

  mat-icon {
    width: 17px;
    padding-bottom: 1px;
  }

  ion-icon {
    width: 17px;
    padding-bottom: 1px;
  }
}
