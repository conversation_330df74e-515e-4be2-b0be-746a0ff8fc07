{"ConnectionStrings": {"ConfigurationDbConnection": "data source=cbos01.southafricanorth.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=edgefactor;password=***********;TrustServerCertificate=True", "PersistedGrantDbConnection": "data source=cbos01.southafricanorth.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=edgefactor;password=***********;TrustServerCertificate=True", "IdentityDbConnection": "data source=cbos01.southafricanorth.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=edgefactor;password=***********;TrustServerCertificate=True", "AdminLogDbConnection": "data source=cbos01.southafricanorth.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=edgefactor;password=***********;TrustServerCertificate=True", "AdminAuditLogDbConnection": "data source=cbos01.southafricanorth.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=edgefactor;password=***********;TrustServerCertificate=True", "DataProtectionDbConnection": "data source=cbos01.southafricanorth.cloudapp.azure.com,1433;initial catalog=EdgeFactor_ID;persist security info=True;user id=edgefactor;password=***********;TrustServerCertificate=True"}, "SeedConfiguration": {"ApplySeed": true}, "DatabaseMigrationsConfiguration": {"ApplyDatabaseMigrations": true}, "DatabaseProviderConfiguration": {"ProviderType": "SqlServer"}, "AdminConfiguration": {"PageTitle": "EdgeFactor.ID", "FaviconUri": "~/favicon.ico", "IdentityAdminRedirectUri": "https://localhost:44303/signin-oidc", "IdentityServerBaseUrl": "https://localhost:44310", "IdentityAdminCookieName": "IdentityServerAdmin", "IdentityAdminCookieExpiresUtcHours": 12, "RequireHttpsMetadata": false, "TokenValidationClaimName": "name", "TokenValidationClaimRole": "role", "ClientId": "EFAdmin", "ClientSecret": "KM8d#W?EMpt7?j5", "OidcResponseType": "code", "Scopes": ["openid", "profile", "email", "roles"], "AdministrationRole": "Admin", "HideUIForMSSqlErrorLogging": false}, "SecurityConfiguration": {"CspTrustedDomains": ["fonts.googleapis.com", "fonts.gstatic.com", "www.gravatar.com"]}, "SmtpConfiguration": {"Host": "", "Login": "", "Password": ""}, "SendGridConfiguration": {"ApiKey": "", "SourceEmail": "", "SourceName": ""}, "AuditLoggingConfiguration": {"Source": "IdentityServer.Admin.Web", "SubjectIdentifierClaim": "sub", "SubjectNameClaim": "name", "IncludeFormVariables": false}, "CultureConfiguration": {"Cultures": [], "DefaultCulture": null}, "HttpConfiguration": {"BasePath": ""}, "IdentityOptions": {"Password": {"RequiredLength": 8}, "User": {"RequireUniqueEmail": true}, "SignIn": {"RequireConfirmedAccount": false}}, "DataProtectionConfiguration": {"ProtectKeysWithAzureKeyVault": false}, "AzureKeyVaultConfiguration": {"AzureKeyVaultEndpoint": "", "ClientId": "", "ClientSecret": "", "TenantId": "", "UseClientCredentials": true, "DataProtectionKeyIdentifier": "", "ReadConfigurationFromKeyVault": false}}