import { Component, OnDestroy, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IContent, ICustomContentDetail } from '@app/core/contracts/contract';
import { ModalController } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-add-custom-content',
    templateUrl: './add-custom-content.component.html',
    styleUrls: ['./add-custom-content.component.scss'],
    standalone: false
})
export class AddCustomContentComponent implements OnInit, OnDestroy {
  customContentForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formIsValid = false;
  rowId: string;
  customItem: IContent;
  isDeleted = false;
  constructor(
    private formBuilder: UntypedFormBuilder,
    private modalController: ModalController
  ) {}

  ngOnInit() {
    this.createForm();
    if (this.customItem) {
      this.setFormValues();
    }
  }

  createForm() {
    this.customContentForm = this.formBuilder.group({
      title: ['', Validators.required],
      description: [''],
      backgroundImageAssetId: [''],
      buttonText: [''],
      buttonUrl: [''],
      contentOrder: ['1', Validators.required],
      sameUrlNavigation: [false],
    });
  }

  setFormValues() {
    if (!this.customContentForm) {
      return;
    }
    this.customContentForm.controls.title.setValue(this.customItem.instanceName);
    this.customContentForm.controls.description.setValue(this.customItem.instanceDescription);
    this.customContentForm.controls.backgroundImageAssetId.setValue(this.customItem.iconAssetId);
    this.customContentForm.controls.buttonText.setValue(this.customItem.action);
    this.customContentForm.controls.buttonUrl.setValue(this.customItem.actionUrl);
    this.customContentForm.controls.contentOrder.setValue(this.customItem.contentOrder);
    this.customContentForm.controls.sameUrlNavigation.setValue(this.customItem.sameUrlNavigation);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.customContentForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {});
  }

  deleteContent() {
    this.isDeleted = true;
    this.saveContent();
  }

  saveContent() {
    const detail: ICustomContentDetail = {
      rowId: this.rowId,
      contentId: this.customItem ? this.customItem.id : null,
      title: this.customContentForm.value.title,
      description: this.customContentForm.value.description,
      backgroundImageAssetId: this.customContentForm.value.backgroundImageAssetId,
      buttonText: this.customContentForm.value.buttonText,
      buttonUrl: this.customContentForm.value.buttonUrl,
      contentOrder: this.customContentForm.value.contentOrder,
      isDeleted: this.isDeleted,
      sameUrlNavigation: this.customContentForm.value.sameUrlNavigation,
    };

    this.modalController.dismiss(detail);
  }

  cancel() {
    this.modalController.dismiss();
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
