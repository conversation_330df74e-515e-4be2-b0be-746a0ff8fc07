<div [ngClass]="{ 'question-container': isKnowledgeQuestion === false }">
  <div [ngClass]="{ 'center-container': isKnowledgeQuestion === false }">
    <ion-item class="spacing" [ngClass]="{ 'grey-border': isKnowledgeQuestion === true }">
      @if (!isShort) {
        <ion-textarea style="--background-color:{{ backgroundColor }};" [(ngModel)]="value" (ionInput)="saveText($event)" [disabled]="question?.userAnswer?.isSubmitted === true"></ion-textarea>
      }
      @if (isShort) {
        <ion-input
          [placeholder]="placeholder"
          style="--background-color:{{ backgroundColor }};"
          [(ngModel)]="value"
          (ionInput)="saveText($event)"
          [disabled]="question?.userAnswer?.isSubmitted === true"></ion-input>
      }
    </ion-item>
  </div>
</div>
