import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-media-and-text-field-editor',
    templateUrl: './media-and-text-field-editor.component.html',
    styleUrls: ['./media-and-text-field-editor.component.scss'],
    standalone: false
})
export class MediaAndTextFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  mediaAndTextForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.mediaAndTextForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }

      this.setFormValues();
    }
  }
  setTypeBwValues(typeBw: number | undefined) {
    if (typeBw !== undefined) {
      return this.builderService.acceptedImageAndVideoFormats.filter(x => Number(x.id) & (typeBw ?? 0)).map(x => x.id);
    }
    return null;
  }
  createForm() {
    this.mediaAndTextForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      description: [this.component?.templateField?.helpDescription],
      caption: [this.component?.templateField?.helpTitle],
      defaultImageUrl: [this.component?.templateField?.defaultImageUrl],
      fileTypeBw: [this.setTypeBwValues(this.component?.templateField?.fileTypeBw), Validators.required],
      minFileSize: [this.component?.templateField?.minFileSize],
      maxFileSize: [this.component?.templateField?.maxFileSize],
      rowNumber: [this.component?.builderRowNumber ?? 0],
      hoverSortOrder: [this.component?.hoverSortOrder ?? 0],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      label1: [this.component?.templateField?.label1, Validators.required],
      placeHolder1: [this.component?.templateField?.placeHolder1],
      caption1: [this.component?.templateField?.caption1],
      description1: [this.component?.templateField?.description1],
      label2: [this.component?.templateField?.label2, Validators.required],
      placeHolder2: [this.component?.templateField?.placeHolder2],
      default2: [this.component?.templateField?.default2],
      caption2: [this.component?.templateField?.caption2],
      description2: [this.component?.templateField?.description2],
      label3: [this.component?.templateField?.label3, Validators.required],
      placeHolder3: [this.component?.templateField?.placeHolder3],
      default3: [this.component?.templateField?.default3],
      caption3: [this.component?.templateField?.caption3],
      description3: [this.component?.templateField?.description3],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.mediaAndTextForm) {
      return;
    }

    this.mediaAndTextForm.controls.label.setValue(this.component.templateField.label);
    this.mediaAndTextForm.controls.description.setValue(this.component.templateField.helpDescription);
    this.mediaAndTextForm.controls.caption.setValue(this.component.templateField.helpTitle);
    this.mediaAndTextForm.controls.defaultImageUrl.setValue(this.component.templateField.defaultImageUrl);
    this.mediaAndTextForm.controls.fileTypeBw.setValue(this.component.templateField.fileTypeBw);
    this.mediaAndTextForm.controls.minFileSize.setValue(this.component.templateField.minFileSize);
    this.mediaAndTextForm.controls.maxFileSize.setValue(this.component.templateField.maxFileSize);
    this.mediaAndTextForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.mediaAndTextForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.mediaAndTextForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.mediaAndTextForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.mediaAndTextForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.mediaAndTextForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.mediaAndTextForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.mediaAndTextForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.mediaAndTextForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.mediaAndTextForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);

    this.mediaAndTextForm.controls.label1.setValue(this.component?.templateField?.label1);
    this.mediaAndTextForm.controls.placeHolder1.setValue(this.component?.templateField?.placeHolder1);
    this.mediaAndTextForm.controls.caption1.setValue(this.component?.templateField?.caption1);
    this.mediaAndTextForm.controls.description1.setValue(this.component?.templateField?.description1);

    this.mediaAndTextForm.controls.labe2.setValue(this.component?.templateField?.label2);
    this.mediaAndTextForm.controls.placeHolder2.setValue(this.component?.templateField?.placeHolder2);
    this.mediaAndTextForm.controls.default2.setValue(this.component?.templateField?.default2);
    this.mediaAndTextForm.controls.caption2.setValue(this.component?.templateField?.caption2);
    this.mediaAndTextForm.controls.description2.setValue(this.component?.templateField?.description2);

    this.mediaAndTextForm.controls.label3.setValue(this.component?.templateField?.label3);
    this.mediaAndTextForm.controls.placeHolder3.setValue(this.component?.templateField?.placeHolder3);
    this.mediaAndTextForm.controls.default3.setValue(this.component?.templateField?.default3);
    this.mediaAndTextForm.controls.caption3.setValue(this.component?.templateField?.caption3);
    this.mediaAndTextForm.controls.description3.setValue(this.component?.templateField?.description3);

    this.mediaAndTextForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth ?? false);
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.mediaAndTextForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.mediaAndTextForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.mediaAndTextForm.valid) {
      this.component.templateField.label = this.mediaAndTextForm.controls.label.value;
      this.component.templateField.helpDescription = this.mediaAndTextForm.controls.description.value;
      this.component.templateField.helpTitle = this.mediaAndTextForm.controls.caption.value;
      this.component.templateField.defaultImageUrl = this.mediaAndTextForm.controls.defaultImageUrl.value;
      this.component.templateField.fileTypeBw = this.mediaAndTextForm.controls.fileTypeBw.value.reduce((a: number, b: number) => a + b, 0);
      this.component.templateField.minFileSize = this.mediaAndTextForm.controls.minFileSize.value;
      this.component.templateField.maxFileSize = this.mediaAndTextForm.controls.maxFileSize.value;
      this.component.builderRowNumber = this.mediaAndTextForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.mediaAndTextForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.mediaAndTextForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.mediaAndTextForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.mediaAndTextForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.mediaAndTextForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.mediaAndTextForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.mediaAndTextForm.controls.isViewField.value;
      this.component.templateField.colspan = this.mediaAndTextForm.controls.colspan.value;
      this.component.templateField.colNumber = this.mediaAndTextForm.controls.colNumber.value;
      this.component.templateField.label1 = this.mediaAndTextForm.controls.label1.value;
      this.component.templateField.placeHolder1 = this.mediaAndTextForm.controls.placeHolder1.value;
      this.component.templateField.caption1 = this.mediaAndTextForm.controls.caption1.value;
      this.component.templateField.description1 = this.mediaAndTextForm.controls.description1.value;
      this.component.templateField.label2 = this.mediaAndTextForm.controls.label2.value;
      this.component.templateField.placeHolder2 = this.mediaAndTextForm.controls.placeHolder2.value;
      this.component.templateField.default2 = this.mediaAndTextForm.controls.default2.value;
      this.component.templateField.caption2 = this.mediaAndTextForm.controls.caption2.value;
      this.component.templateField.description2 = this.mediaAndTextForm.controls.description2.value;
      this.component.templateField.label3 = this.mediaAndTextForm.controls.label3.value;
      this.component.templateField.placeHolder3 = this.mediaAndTextForm.controls.placeHolder3.value;
      this.component.templateField.default3 = this.mediaAndTextForm.controls.default3.value;
      this.component.templateField.caption3 = this.mediaAndTextForm.controls.caption3.value;
      this.component.templateField.description3 = this.mediaAndTextForm.controls.description3.value;
      this.component.templateField.useMaxWidth = this.mediaAndTextForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
