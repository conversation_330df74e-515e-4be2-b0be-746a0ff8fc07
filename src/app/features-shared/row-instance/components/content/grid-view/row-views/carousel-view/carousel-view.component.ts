import { moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, ElementRef, EventEmitter, Input, OnChanges, Output, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { IContent } from '@app/core/contracts/contract';
import { LayoutService } from '@app/core/services/layout-service';
import { RowService } from '@app/core/services/row.service';
import { SwiperOptions } from 'swiper/types';

@Component({
    selector: 'app-carousel-view',
    templateUrl: './carousel-view.component.html',
    styleUrls: ['./carousel-view.component.scss'],
    standalone: false
})
export class CarouselViewComponent implements OnChanges {
  @Input() thumbnailTypesTemplate: TemplateRef<any>;
  @Input() aspectRatio: string;
  @Input() rowWidth: number;
  @Input() content: IContent[];
  @Input() isEducator: boolean;
  @Input() hasAdminAccess: boolean;
  @Input() hideAddButton: boolean;
  @Output() contentOrderChanged: EventEmitter<IContent[]> = new EventEmitter<IContent[]>();
  @ViewChild('swiper') swiperRef: ElementRef;
  swiperConfig: SwiperOptions = {
    slidesPerView: 4,
    spaceBetween: 11,
    allowTouchMove: true,
    pagination: {
      el: '.swiper-pagination',
      clickable: true,
      dynamicBullets: true,
      dynamicMainBullets: 4,
    },
  };

  get hidePrevNavButton() {
    return this.swiperRef?.nativeElement?.swiper?.isBeginning ?? false;
  }

  get hideNextNavButton() {
    return this.swiperRef?.nativeElement?.swiper?.isEnd ?? false;
  }

  get mobileScreen() {
    return this.layoutService.currentScreenSize === 'xs';
  }

  constructor(
    private rowService: RowService,
    private layoutService: LayoutService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['aspectRatio'] || changes['rowWidth']) {
      const rowData = this.rowService.setupVariables(this.aspectRatio, this.rowWidth);
      if (!rowData) return;

      if (this.mobileScreen && rowData.columnCount > 1) {
        rowData.columnCount += 0.3;
      }
      else if (this.mobileScreen && (rowData.type === 'Text container' || rowData.type === 'Narrow container')) {
        rowData.columnCount += 0.1;
      }

      if (this.swiperRef) {
        this.swiperRef.nativeElement.slidesPerView = rowData.columnCount;
      } else {
        this.swiperConfig.slidesPerView = rowData.columnCount;
      }
    }
  }

  slideNext() {
    this.swiperRef.nativeElement.swiper.slideNext(100);
  }

  slidePrev() {
    this.swiperRef.nativeElement.swiper.slidePrev(100);
  }

  dropListDropped(event: any) {
    moveItemInArray(this.content, event.previousIndex, event.currentIndex);
    this.contentOrderChanged.emit(this.content);
  }
}
