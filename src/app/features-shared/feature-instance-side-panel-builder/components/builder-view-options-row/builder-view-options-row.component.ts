import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { IInstance, IKeyValue } from '@app/core/contracts/contract';
import { BuilderTypeOptions } from '@app/core/enums/builder-view-options.enum';
import { ConfirmationDialogComponent } from '@app/standalone/modals/confirmation-dialog/confirmation-dialog.component';
import { OptionSelectorDialogComponent } from '@app/standalone/modals/option-selector-dialog/option-selector-dialog.component';
import { ModalController, PopoverController } from '@ionic/angular';
import { OverlayEventDetail } from '@ionic/core';
import { RolesService } from '@app/core/services/roles.service';
import { ActionTypes } from '@app/core/enums/action-types.enum';

@Component({
    selector: 'app-builder-view-options-row',
    templateUrl: './builder-view-options-row.component.html',
    styleUrls: ['./builder-view-options-row.component.scss'],
    standalone: false
})
export class BuilderViewOptionsRowComponent implements OnInit {
  @Output() publishClicked = new EventEmitter<string>();
  @Input() publishDisabled: boolean;
  @Input() instance?: IInstance;
  @Input() showPublishedOptions = true;

  typeOptions = BuilderTypeOptions;
  publishButtonText: string;
  currentCustomUrl: string;

  constructor(
    private popOver: PopoverController,
    private modalController: ModalController,
    private router: Router,
    private location: Location,
    private rolesService: RolesService
  ) {}

  ngOnInit(): void {
    this.checkFeatureTypes();

    if (this.showPublishedOptions) {
      this.setPublishText();
    }
  }

  checkFeatureTypes() {
    switch (this.instance?.feature?.featureType?.name) {
      case 'Product Manager':
      case 'Question Manager':
      case 'Organization Manager':
      case 'Media Manager':
      case 'User Manager':
      case 'Network Manager':
      case 'Communication Manager':
        this.showPublishedOptions = false;
        break;
      default:
        this.showPublishedOptions = true;
        break;
    }
  }

  setPublishText() {
    if (this.instance?.status) {
      this.publishButtonText = this.instance.status !== 'private' ? 'unpublish' : 'publish';
    } else {
      this.publishButtonText = 'unpublish';
    }
  }

  async openPublishOptions(event: any, status: string | undefined) {
    if (status && status === 'private') {
      if (this.instance?.feature.featureType.name === 'Modifiable Learning Container Pages') {
        await this.openPublishDialog('organization');
        return;
      }
      const options: IKeyValue<string, string>[] = [
        { key: 'organization', value: 'To my organization' },
        { key: 'public', value: 'Publicly' },
        { key: 'network', value: 'To my network' },
      ];

      const popover = await this.popOver.create({
        component: OptionSelectorDialogComponent,
        cssClass: 'question-type-popover',
        componentProps: {
          options: options,
        },
        event: event,
        side: 'bottom',
      });

      popover.onDidDismiss().then(async (overlayEventDetail: OverlayEventDetail) => {
        if (overlayEventDetail.data) {
          await this.openPublishDialog(overlayEventDetail.data.key);
        }
      });

      await popover.present();
    } else {
      this.publishClicked.emit('private');
      this.appendUnpublishedToUrl();
      this.publishButtonText = 'publish';
    }
  }

  async openPublishDialog(status: string) {
    const modal = await this.modalController.create({
      component: ConfirmationDialogComponent,
      cssClass: 'confirm-dialog',
      componentProps: {
        headerText: 'Update your page',
        bodyText: `You're about to update your ${this.instance?.feature?.title}. Once you publish your work it will be visible to enrolled people and other users at your Organization.`,
        buttonText: 'Publish',
      },
    });

    modal.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
      if (overlayEventDetail.role === 'confirm') {
        this.publishClicked.emit(status);
        this.removeUnpublishedFromUrl();
        this.publishButtonText = 'unpublish';
      }
    });

    await modal.present();
  }

  hasEditAccess() {
    return this.rolesService.hasFeatureRoleAccess([ActionTypes.Manage, ActionTypes.Publish]);
  }

  private appendUnpublishedToUrl(): void {
    const currentUrl = this.currentCustomUrl || this.router.url;
    if (!currentUrl.endsWith('/unpublished')) {
      this.currentCustomUrl = currentUrl.endsWith('/') ? `${currentUrl}unpublished` : `${currentUrl}/unpublished`;
      this.location.go(this.currentCustomUrl);
    }
  }

  private removeUnpublishedFromUrl(): void {
    let currentUrl = this.currentCustomUrl || this.router.url;
    if (currentUrl.endsWith('/unpublished')) {
      currentUrl = currentUrl.replace('/unpublished', '');
      this.location.go(currentUrl);
      this.currentCustomUrl = currentUrl;
    }
  }
}
