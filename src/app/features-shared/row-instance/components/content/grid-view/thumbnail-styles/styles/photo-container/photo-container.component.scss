.layout {
  background-color: #111;
  background-image: var(--background-image);
  background-size: cover !important;
  width: 100%;
  height: 100%;
  border-radius: 0.5em;
  min-height: 0;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  aspect-ratio: 9 / 2;
}

ion-button {
  --background: lightgray;
}

ion-button:hover {
  --background: #f99e00;
}

.icon-container {
  display: flex;
  top: 5px;
  position: absolute;
  gap: 5px;
  z-index: 2;
}

.icon-container-left {
  left: 5px;
}

.icon-container-right {
  right: 5px;
}

.icon {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-radius: 50%;
  height: 25px;
  width: 25px;
  min-height: 20px;
  min-width: 20px;

  mat-icon {
    width: 17px;
    padding-bottom: 1px;
  }
}
