@if (question && questionTypes) {
  <ion-grid class="question-grid">
    <ion-row>
      <ion-col [size]="12">
        <div [ngClass]="{ 'question-container left-margin': onlyQuestion !== true, onlyQuestion: onlyQuestion === true }">
          @if (onlyQuestion !== true) {
            <ion-select interface="popover" #select (ionChange)="setQuestionType($event)" [value]="selectedQuestionTypeId">
              @for (type of questionTypes; track type) {
                <ion-select-option [value]="type.id">{{ type.name }} </ion-select-option>
              }
            </ion-select>
          }
          <div class="inner-container">
            @if (onlyQuestion !== true) {
              <ion-label position="stacked"> Question Text </ion-label>
            }
            <div class="quill-container">
              <app-content-quill-editor
                [placeHolder]="question?.questionText ?? component?.templateField?.helpTitle ?? ''"
                [disabled]="false"
                (dataChanged)="setQuestionText($event)"
                [value]="question.questionText"></app-content-quill-editor>
              @if (errorMessage && touched) {
                <ion-text>
                  {{ errorMessage }}
                </ion-text>
              }
            </div>
            @if (question.questionType.name === 'Opinion Scale' || question.questionType.name === 'Interest Profiler') {
              <app-opinion-scale-settings [question]="question" (questionChanged)="questionUpdate()"></app-opinion-scale-settings>
            }
            @if (question.questionType.name === 'RIASEC Interest Profiler') {
              <app-riasec-interest-profiler-settings [question]="question" (questionChanged)="questionUpdate()"></app-riasec-interest-profiler-settings>
            }
          </div>
          @if (question) {
            <ion-item>
              @if (question.questionType.name.indexOf('Multiple Choice') !== -1 && question.isOptionPhoto) {
                <app-option-photo-answer [question]="question" (questionChanged)="updateValue()"></app-option-photo-answer>
              }
              @if (
                (question.questionType.name.indexOf('Multiple Choice') !== -1 && !question.isOptionPhoto) ||
                question.questionType.name === 'Dropdown' ||
                question.questionType.name.indexOf('Picture Choice') !== -1
              ) {
                <app-multiple-choice-answer [question]="question" (questionChanged)="updateValue()"></app-multiple-choice-answer>
              }
              @if (question.questionType.name === 'Matching List') {
                <app-matching-list class="full-width" [question]="question" (questionChanged)="updateValue()"></app-matching-list>
              }
              @if (question.questionType.name === 'Picture Matching') {
                <app-picture-matching class="full-width" [question]="question" (questionChanged)="updateValue()"></app-picture-matching>
              }
              @if (question.questionType.name === 'Text to Picture') {
                <app-text-to-picture-matching class="full-width" [question]="question" (questionChanged)="updateValue()"></app-text-to-picture-matching>
              }
              @if (question.questionType.name === 'True/False') {
                <app-polar-answer [options]="['True', 'False']" [question]="question" (questionChanged)="updateValue()"></app-polar-answer>
              }
              @if (question.questionType.name === 'Yes/No') {
                <app-polar-answer [options]="['Yes', 'No']" [question]="question" (questionChanged)="updateValue()"></app-polar-answer>
              }
              @if (question.questionType.name === 'Opinion Scale' || question.questionType.name === 'Interest Profiler') {
                <app-opinion-scale-answer class="full-width" [question]="question"></app-opinion-scale-answer>
              }
            </ion-item>
          }
          <div class="inner-container">
            @if (question.questionType.name.indexOf('Multiple Choice') !== -1 || question.questionType.name === 'Dropdown') {
              <app-multiple-choice-settings [question]="question" (questionChanged)="questionUpdate()"></app-multiple-choice-settings>
            }
            @if (question.questionType.name === 'Matching List') {
              <app-matching-list-settings [question]="question" (questionChanged)="questionUpdate()"></app-matching-list-settings>
            }
            @if (question.questionType.name === 'Picture Matching') {
              <app-picture-matching-settings [question]="question" (questionChanged)="questionUpdate()"></app-picture-matching-settings>
            }
            @if (question.questionType.name === 'Text to Picture') {
              <app-text-to-picture-matching-settings [question]="question" (questionChanged)="questionUpdate()"></app-text-to-picture-matching-settings>
            }
            <app-text-input-control
              [placeHolder]="question.answerText ?? ''"
              [disabled]="false"
              [label]="'Answer Text (Optional)'"
              (textChanged)="setQuestionAnswerText($event)"
              [defaultValue]="question.answerText ?? ''"></app-text-input-control>
            @if (errorMessage && touched) {
              <ion-text>
                {{ errorMessage }}
              </ion-text>
            }
          </div>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
}
