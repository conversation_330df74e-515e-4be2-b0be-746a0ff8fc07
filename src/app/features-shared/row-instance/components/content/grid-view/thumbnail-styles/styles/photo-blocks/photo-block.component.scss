.layout {
  background-image: var(--background-image);
  background-repeat: no-repeat;
  background-size: cover !important;
  background-position: center center !important;
  width: 100%;
  border-radius: 0.5em;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  aspect-ratio: 1 / 1;
  background: linear-gradient(to bottom,rgba(0,0,0,0)55%,rgba(0,0,0,0.1)60%,rgba(0,0,0,0.65)75%,rgba(0,0,0,.8)81%,rgba(0,0,0,0.9)89%),var(--background-image) no-repeat;
}

.text-container {
  width: 100%;
  text-align: center;
  font-family: 'roboto';
  line-height: 1.1;
  letter-spacing: 0.01em;

  h1 {
    color: #fff;
    font-weight: 900;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  p {
    color: #aaa;
    text-overflow: ellipsis;
    font-style: italic;
    font-weight: 500;
  }
}

@media (min-width: 1501px) {
  .text-container {
    padding: 15px 15px 0px 15px;

    h1 {
      font-size: 1.25em;
    }

    p {
      font-size: 0.875em;
    }
  }
}

@media (min-width: 1281px) and (max-width: 1500px) {
  .text-container {
    padding: 15px 15px 0px 15px;

    h1 {
      font-size: 1.125em;
    }

    p {
      font-size: 0.875em;
    }
  }
}

@media (min-width: 769px) and (max-width: 1280px) {
  .text-container {
    padding: 15px 15px 0px 15px;

    h1 {
      font-size: 1.063em;
    }

    p {
      font-size: 0.875em;
    }
  }
}

@media (min-width: 480px) and (max-width: 768px) {
  .text-container {
    padding: 15px 15px 0px 15px;

    h1 {
      font-size: 1em;
    }

    p {
      font-size: 0.813em;
    }
  }
}

@media (max-width: 479px) {
  .text-container {
    padding: 40px 15px 0px 15px;

    h1 {
      font-size: 1em;
    }

    p {
      font-size: 0.813em;
    }
  }
}

ion-button {
  --background: lightgray;
  font-weight: 600;
}

ion-button:hover {
  --background: #f99e00;
}
