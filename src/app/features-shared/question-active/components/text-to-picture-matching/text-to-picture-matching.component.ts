import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { IQuestion, IMatchingItem, IMatchingItemPrompt, IQuestionAnswer } from '@app/core/contracts/contract';
import { Subject } from 'rxjs';

@Component({
    selector: 'app-text-to-picture-matching',
    templateUrl: './text-to-picture-matching.component.html',
    styleUrls: ['./text-to-picture-matching.component.scss'],
    standalone: false
})
export class TextToPictureMatchingComponent implements OnInit, OnDestroy {
  @Input() sidePanelPadding = false;
  @Input() question: IQuestion;
  @Output() questionChanged = new EventEmitter<any>();

  componentDestroyed$: Subject<boolean> = new Subject();
  matchingItem: IMatchingItem;

  ngOnInit() {
    this.setMatchingItem();
  }

  setMatchingItem() {
    if (this.question.questionAnswers[0]?.value) {
      this.matchingItem = JSON.parse(this.question.questionAnswers[0]?.value) as IMatchingItem;
    } else {
      this.matchingItem = {} as IMatchingItem;
      this.matchingItem.itemPrompts = [];
      this.setDefaultFirstValue();
    }
  }

  setDefaultFirstValue() {
    if (this.matchingItem?.itemPrompts.length === 0) {
      this.addPromptItem();
    }
    if (this.question.questionAnswers.length === 0) {
      this.addQuestionAnswers();
    }
  }

  saveInputTextDirect(event: any, type: string, item: IMatchingItemPrompt | null) {
    if (item != null) {
      const index = this.matchingItem.itemPrompts.findIndex(x => x.sortOrder === item.sortOrder);
      if (index !== -1) {
        if (type === 'promptTitle') {
          this.matchingItem.itemPrompts[index].promptTitle = event ?? '';
        } else if (type === 'answerTitle') {
          this.matchingItem.itemPrompts[index].answerTitle = event.detail.value;
        }
      }
      this.updateMatchingItem();
    }
  }

  updateMatchingItem() {
    const answer: IQuestionAnswer = {
      id: '',
      questionId: this.question.id,
      tagId: null,
      title: 'New Answer',
      value: JSON.stringify(this.matchingItem),
      sortOrder: this.question.questionAnswers.length + 1,
      assetId: null,
      isCorrect: false,
      isSelected: null,
      tag: null,
    };
    this.question.questionAnswers[0] = answer;
    this.questionChanged.next(this.question);
  }

  setMatchingPromptItemSortOrder() {
    //SortOrder = IndexPosition.
    if (this.matchingItem.itemPrompts.length > 0) {
      this.matchingItem.itemPrompts.forEach((item, index) => {
        item.sortOrder = index;
      });
    }
  }

  addQuestionAnswers() {
    const answer: IQuestionAnswer = {
      id: '',
      questionId: this.question.id,
      tagId: null,
      title: 'New Answer',
      value: '',
      sortOrder: this.question.questionAnswers.length + 1,
      assetId: null,
      isCorrect: false,
      isSelected: null,
      tag: null,
    };
    this.question.questionAnswers.push(answer);
  }

  addPromptItem() {
    const newPromptItem: IMatchingItemPrompt = {
      promptTitle: '',
      answerTitle: '',
      sortOrder: 0,
    };

    this.matchingItem.itemPrompts.push(newPromptItem);
    this.setMatchingPromptItemSortOrder();
  }

  removePromptItem(item: IMatchingItemPrompt) {
    const index = this.matchingItem.itemPrompts.findIndex(x => x.sortOrder === item.sortOrder);
    this.matchingItem.itemPrompts.splice(index, 1);
    this.setMatchingPromptItemSortOrder();
    this.updateMatchingItem();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
