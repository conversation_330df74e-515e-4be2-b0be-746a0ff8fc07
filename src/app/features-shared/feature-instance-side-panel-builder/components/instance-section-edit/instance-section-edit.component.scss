.parent-form-container {
  margin: 10px 16px 10px 16px;
  .back-col {
    display: flex;
    justify-content: flex-start;

    ion-button.button::part(native) {
      line-height: 0px;
    }
  }

  .top-header-col {
    margin-top: 10px;
    .header {
      color: white;
      font-family: '<PERSON><PERSON>';
      font-weight: bold;
      font-size: 25px;
    }
  }

  .middle-line {
    margin-top: 20px;
    hr {
      border-top: 3px solid rgb(40, 40, 40);
    }
  }

  .toggle-row {
    padding-top: 20px;
    .toggle-col {
      display: flex;

      align-items: center;
      .mat-mdc-slide-toggle {
        vertical-align: middle;
        margin-right: 15px;
      }
      ion-label {
        font-size: 18px;
        color: white;
        font-family: '<PERSON><PERSON>';
        font-weight: bold;
      }
    }
  }
}

.no-margin {
  margin: 0px !important;
}
