﻿// Copyright (c) <PERSON> & <PERSON>. All rights reserved.
// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.

// Original file: https://github.com/IdentityServer/IdentityServer4.Quickstart.UI
// Modified by <PERSON>

using EdgeFactor.ID.STS.Identity.ViewModels.Account;
using IdentityServer4.Models;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Text.Json;

namespace EdgeFactor.ID.STS.Identity.Helpers
{
    public static class Extensions
    {
        /// <summary>
        /// Checks if the redirect URI is for a native client.
        /// </summary>
        /// <returns></returns>
        public static bool IsNativeClient(this AuthorizationRequest context)
        {
            return !context.RedirectUri.StartsWith("https", StringComparison.Ordinal)
                   && !context.RedirectUri.StartsWith("http", StringComparison.Ordinal);
        }

        public static IActionResult LoadingPage(this Controller controller, string viewName, string redirectUri)
        {
            controller.HttpContext.Response.StatusCode = 200;
            controller.HttpContext.Response.Headers["Location"] = "";

            return controller.View(viewName, new RedirectViewModel { RedirectUrl = redirectUri });
        }

        private static readonly JsonSerializerOptions s_defaultSerializerSettings = new JsonSerializerOptions()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };
        public static T Deserialize<T>(this string json)
        {
            var x = JsonSerializer.Deserialize<T>(json, s_defaultSerializerSettings);
            var p = x.GetType().GetProperties().FirstOrDefault();
            var t = p.GetValue(x);

            if (t == null || t.ToString() == "0")
            {
                return JsonSerializer.Deserialize<T>(json);
            }
            return JsonSerializer.Deserialize<T>(json, s_defaultSerializerSettings);
        }
    }
}








