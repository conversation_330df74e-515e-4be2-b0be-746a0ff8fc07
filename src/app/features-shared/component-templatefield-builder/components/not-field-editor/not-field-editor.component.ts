import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-not-field-editor',
    templateUrl: './not-field-editor.component.html',
    styleUrls: ['./not-field-editor.component.scss'],
    standalone: false
})
export class NotFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  authoringForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.authoringForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }

      this.setFormValues();
    }
  }

  createForm() {
    this.authoringForm = this.formBuilder.group({
      rowNumber: [this.component?.builderRowNumber ?? 0],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.authoringForm) {
      return;
    }

    this.authoringForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.authoringForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.authoringForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.authoringForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.authoringForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.authoringForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.authoringForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.authoringForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.authoringForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.authoringForm.valid) {
      this.component.builderRowNumber = this.authoringForm.controls.rowNumber.value;
      this.component.instanceSortOrder = this.authoringForm.controls.instanceSortOrder.value;
      this.component.templateField.isVisibleRepository = this.authoringForm.controls.isVisibleRepository.value;
      this.component.templateField.isPreviewField = this.authoringForm.controls.isPreviewField.value;
      this.component.templateField.isViewField = this.authoringForm.controls.isViewField.value;
      this.component.templateField.colspan = this.authoringForm.controls.colspan.value;
      this.component.templateField.colNumber = this.authoringForm.controls.colNumber.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
