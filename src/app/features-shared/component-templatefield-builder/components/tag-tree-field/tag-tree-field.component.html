@if (templateField) {
  <div class="field-container">
    <div class="heading-container">
      <ion-label>
        Select Tree
        <span class="reqAsterisk">
          <span>* </span>
          <ion-icon name="information-circle-outline"></ion-icon>
        </span>
      </ion-label>
    </div>
    <div class="tags-container">
      <ion-radio-group class="properties-container" [value]="templateField?.tagTreeId">
        @for (tag of tags; track tag) {
          <div class="property">
            <div class="property-left" (click)="setPropertyTag(tag)">
              <ion-radio color="warning" [value]="tag.id"></ion-radio>
              <h2 class="propertyName" matTooltip="{{ tag?.name }}">{{ tag?.name }}</h2>
            </div>
            <div class="row">
              <ion-label> Limit to: </ion-label>
              @if (tag.id === templateField?.tagTreeId) {
                <ion-select class="ion-no-padding" interface="popover" #select [value]="templateField?.limitTo" (ionChange)="setPropertyLevel($event)">
                  @for (level of getLevels(tag); track level) {
                    <ion-select-option [value]="level"> T{{ level }} </ion-select-option>
                  }
                </ion-select>
              }
              @if (tag.id !== templateField?.tagTreeId) {
                <ion-select interface="popover" disabled="{{ tag.id !== templateField?.tagTreeId }}" #select (ionChange)="setPropertyLevel($event)">
                  @for (level of getLevels(tag); track level) {
                    <ion-select-option [value]="level"> T{{ level }} </ion-select-option>
                  }
                </ion-select>
              }
            </div>
          </div>
        }
      </ion-radio-group>
    </div>
  </div>
}
