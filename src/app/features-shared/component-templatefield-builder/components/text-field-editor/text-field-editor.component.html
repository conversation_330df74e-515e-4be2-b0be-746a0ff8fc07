@if (textFieldForm) {
  <form [formGroup]="textFieldForm">
    <ion-grid>
      <app-field-editor-base [fieldForm]="textFieldForm"></app-field-editor-base>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Default Value'" [placeHolder]="'Add Default Value'" formControlName="defaultText"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-card>
            <ion-card-content>
              <app-field-checkboxes-base [baseForm]="textFieldForm"></app-field-checkboxes-base>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVariable">Is Variable</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVisibleRepository">Is Visible in Repository</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isInherit">System Property</mat-slide-toggle>
              @if (isInheritControl.value) {
                <app-system-property-selector [templateField]="component.templateField" [formGroup]="textFieldForm"></app-system-property-selector>
              }
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
