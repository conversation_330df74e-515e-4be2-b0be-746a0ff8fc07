.add-content-container {
  padding: 10px;
  .search-container {
    .heading-row {
      width: 100%;
      display: flex;
      justify-content: center;

      .heading-col {
        h5 {
          text-align: center;
          font-weight: bold;
          margin: 0px;
        }
        .under-line {
          hr {
            margin-top: 5px;
            margin-bottom: 0px;
            width: 100%;
            border-top: 1px solid rgb(32, 32, 32);
          }
        }
      }
    }

    .close-icon-row {
      width: 100%;
      display: flex;
      justify-content: flex-end;

      ion-icon {
        color: #f99e00;
        font-size: 25px;
        cursor: pointer;
      }
    }

    ion-searchbar {
      padding: 10px;
    }
  }

  .parent-content-container {
    max-height: 200px;

    ion-card {
      background-color: #1e1e1e;
      border-radius: 5px;
      padding: 5px;

      .thumbnail-col {
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          height: 50px;
          width: 50px;
        }
      }

      .headings-col {
        display: flex;
        flex-direction: column;
        justify-content: center;
        .instance-title {
          color: white;
          font-weight: bold;
        }

        .feature-descripitor {
          .inner-text {
            p {
              margin: 0px !important;
              font-style: italic;
            }
          }
        }
      }
    }
  }
}
