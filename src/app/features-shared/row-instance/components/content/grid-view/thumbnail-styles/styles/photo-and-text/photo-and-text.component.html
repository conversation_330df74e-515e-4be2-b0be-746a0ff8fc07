<div appRowContentHover (rowContentHoverValuesChanged)="updateRowContentHoverValues($event)">
  <div role="button" aria-disabled="true" (click)="route()" (keydown)="route()" class="item">
    @if (!rowContentHoverValues?.showHover) {
      <div
        class="image"
        style="--background-image: url('{{ imageUrl }}')"
        [ngClass]="{
          'status-inprogress': content.status === 'InProgress',
          'status-complete': content.status === 'Completed',
        }">
        <app-thumbnail-icons
          [row]="row"
          [content]="content"
          [instance]="instance"
          [readingMode]="readingMode"
          [isDraggable]="isDraggable"
          [isEducator]="isEducator"
          [hasAdminAccess]="hasAdminAccess"
          [hideAddButton]="hideAddButton"
          [canHover]="true"
          [onHover]="false"
          [isAssignmentRow]="isAssignmentRow"
          (contentRemoved)="emitContentRemoved()"
          (editCustomRowContent)="editContent()">
        </app-thumbnail-icons>
        <div class="gradient-overlay">
          <div class="descriptors-container">
            <div class="title">
              {{ content.instanceName }}
            </div>
            @if (content.entityType === entityTypes.Instances && content.instanceDescriptors) {
              <span class="inner-html">
                <div [innerHTML]="content.instanceDescriptors ?? '' | parsePipe: content.id : null : false | async"></div>
              </span>
            }
          </div>
          @if (content.status) {
            <div class="status-foot-container" loading="lazy">
              @if (content.status === 'InProgress') {
                <span class="inner" style="background-color: orange">
                  <span>IN PROGRESS</span>
                </span>
              }
              @if (content.status === 'Completed') {
                <span class="inner" style="background-color: green">
                  <span>COMPLETED</span>
                </span>
              }
            </div>
          }
          @if (content?.achievementCompletion) {
            <div role="button" aria-disabled="true" class="badge-icon" (click)="openAchievementCompletionModalOnClick($event)" (keydown)="openAchievementCompletionModalOnClick($event)">
              <img ngSrc="{{ badgeImageIcon }}" fill="true" alt />
            </div>
          }
        </div>
      </div>
    }

    @if (rowContentHoverValues?.showHover) {
      <div class="hover-item-container">
        <div
          role="button"
          aria-disabled="true"
          appRowContentHoverForeground
          [hoverValues]="rowContentHoverValues"
          style="--background-image: url('{{ imageUrl }}')"
          [ngClass]="{
            'status-inprogress': content.status === 'InProgress',
            'status-complete': content.status === 'Completed',
            'hover-item-org': content.entityType === entityTypes.Organizations,
            'hover-item-default': content.entityType !== entityTypes.Organizations,
          }"
          class="hover-item hover-image background-layout">
          <div class="actions-heading">
            <app-thumbnail-icons
              [row]="row"
              [content]="content"
              [instance]="instance"
              [readingMode]="readingMode"
              [isDraggable]="isDraggable"
              [isEducator]="isEducator"
              [hasAdminAccess]="hasAdminAccess"
              [hideAddButton]="hideAddButton"
              [canHover]="true"
              [onHover]="true"
              [isAssignmentRow]="isAssignmentRow"
              (contentRemoved)="emitContentRemoved()"
              (editCustomRowContent)="editContent()">
            </app-thumbnail-icons>
          </div>
          <div class="gradient-overlay">
            <!--Features-->
            @if (content.entityType === entityTypes.DefaultInstance) {
              <div class="hover-descriptors-container">
                @if (content.featureName) {
                  <div class="title">{{ content.featureName | uppercase }}</div>
                } @else if (content.instanceName) {
                  <div class="title">{{ content.instanceName }}</div>
                }
                @if (content.featureDescriptors) {
                  <span class="inner-html" [innerHTML]="content.featureDescriptors ?? '' | parsePipe: content.id : null : false | async"></span>
                }
                <div class="description">{{ content.instanceDescription }}</div>
              </div>
            }
            <!--Instances-->
            @if (content.entityType === entityTypes.Instances) {
              <div class="hover-descriptors-container">
                @if (content.instanceName) {
                  <div class="title">{{ content.instanceName }}</div>
                }
                @if (content.instanceDescriptors) {
                  <span class="inner-html" [innerHTML]="content.instanceDescriptors ?? '' | parsePipe: content.id : null : false | async"></span>
                }
                <div class="description">{{ content.instanceDescription }}</div>
              </div>
            }
          </div>
          @if (content.status) {
            <div class="status-foot-container" loading="lazy">
              <div class="instance-tracker-container">
                <app-instance-tracking [instanceId]="content.id" [status]="content.status"></app-instance-tracking>
              </div>
            </div>
          }
          @if (content?.achievementCompletion) {
            <div role="button" aria-disabled="true" class="badge-icon" (click)="openAchievementCompletionModalOnClick($event)" (keydown)="openAchievementCompletionModalOnClick($event)">
              <img ngSrc="{{ badgeImageIcon }}" fill="true" alt />
            </div>
          }
        </div>
      </div>
    }
  </div>
</div>
