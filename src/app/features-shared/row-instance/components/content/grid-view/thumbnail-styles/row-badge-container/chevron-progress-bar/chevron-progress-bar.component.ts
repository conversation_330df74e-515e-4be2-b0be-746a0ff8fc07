import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-chevron-progress-bar',
    templateUrl: './chevron-progress-bar.component.html',
    styleUrls: ['./chevron-progress-bar.component.scss'],
    standalone: false
})
export class ChevronProgressBarComponent implements OnInit {
  @Input() isEnrolled: boolean;
  @Input() progress = 0;
  @Input() chevAmount = 10;
  relationAmount: number;
  counter = Array;
  constructor() {}

  ngOnInit() {
    this.setCounter(this.chevAmount);
    this.setRelationAmount();
  }

  setRelationAmount() {
    this.relationAmount = (this.progress / 100) * this.chevAmount;
  }

  setCounter(length: number) {
    return new Array(length);
  }
}
