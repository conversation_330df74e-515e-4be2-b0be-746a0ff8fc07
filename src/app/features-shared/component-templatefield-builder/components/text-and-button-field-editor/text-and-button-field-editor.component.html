@if (textAndButtonForm) {
  <form class="parent-form-container" [formGroup]="textAndButtonForm">
    <ion-grid>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="header">Edit {{ component.componentType.name }}</div>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Personalize Instance Title</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Add field label...'" formControlName="label1"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Placeholder'" [placeHolder]="'Type here...'" formControlName="placeHolder1"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Default'" [placeHolder]="'Type here...'" formControlName="default1"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Type here...'" formControlName="caption1"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Description'" [placeHolder]="'Type here...'" formControlName="description1"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Personalize Description</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Type here...'" formControlName="label2"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Placeholder'" [placeHolder]="'Type here...'" formControlName="placeHolder2"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Default'" [placeHolder]="'Type here...'" formControlName="default2"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Type here...'" formControlName="caption2"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Description'" [placeHolder]="'Type here...'" formControlName="description2"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Personalize Button</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Type here...'" formControlName="label3"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Placeholder'" [placeHolder]="'Type here...'" formControlName="placeHolder3"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Default'" [placeHolder]="'Type here...'" formControlName="default3"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Type here...'" formControlName="caption3"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Description'" [placeHolder]="'Type here...'" formControlName="description3"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Personalize Url</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Label'" [placeHolder]="'Type here...'" formControlName="label4"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Placeholder'" [placeHolder]="'Type here...'" formControlName="placeHolder4"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Default'" [placeHolder]="'Type here...'" formControlName="default4"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Caption'" [placeHolder]="'Type here...'" formControlName="caption4"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" [label]="'Description'" [placeHolder]="'Type here...'" formControlName="description4"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Hover Sort Order'"
            [placeHolder]="'Add field hover sort order...'"
            formControlName="hoverSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Instance Sort Order'"
            [placeHolder]="'Add field instance sort order...'"
            formControlName="instanceSortOrder"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="row-number-col">
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Row Number'"
            [placeHolder]="'Add field row number...'"
            formControlName="rowNumber"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Column Number (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add column number (Out of 12)...'"
            formControlName="colNumber"
            [type]="'number'">
          </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control
            [noPadding]="true"
            [backgroundColor]="'#292929'"
            [label]="'Colspan (Out of 12)'"
            [limit]="12"
            [placeHolder]="'Add field colspan (Out of 12)...'"
            formControlName="colspan"
            [type]="'number'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="sub-header">Settings</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-field-checkboxes-base [baseForm]="textAndButtonForm"></app-field-checkboxes-base>
          <mat-slide-toggle class="full-width" color="primary" formControlName="isButtonBelowText">Put the button below the text</mat-slide-toggle>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
