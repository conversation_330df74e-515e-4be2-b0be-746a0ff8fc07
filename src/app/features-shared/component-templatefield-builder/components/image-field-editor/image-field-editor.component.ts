import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-image-field-editor',
    templateUrl: './image-field-editor.component.html',
    styleUrls: ['./image-field-editor.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class ImageFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  imageFieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.imageFieldForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  setTypeBwValues(typeBw: number | undefined) {
    if (typeBw !== undefined) {
      return this.builderService.acceptedImageFormats.filter(x => Number(x.id) & typeBw).map(x => x.id);
    }
    return null;
  }

  createForm() {
    this.imageFieldForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      placeHolderText: [this.component?.templateField?.placeHolderText, Validators.required],
      helpTitle: [this.component?.templateField?.helpTitle],
      helpDescription: [this.component?.templateField?.helpDescription],
      defaultImageUrl: [this.component?.templateField?.defaultImageUrl],
      rowNumber: [this.component?.builderRowNumber ?? 0],
      hoverSortOrder: [this.component?.hoverSortOrder ?? 0],
      instanceSortOrder: [this.component?.instanceSortOrder ?? 0],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      fileTypeBw: [this.setTypeBwValues(this.component?.templateField?.fileTypeBw), Validators.required],
      minFileSize: [this.component?.templateField?.minFileSize],
      maxFileSize: [this.component?.templateField?.maxFileSize],
      isInherit: [this.component?.templateField?.isInherit ?? false],
      systemProperty: [this.component?.templateField?.systemProperty?.id],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.imageFieldForm) {
      return;
    }

    this.imageFieldForm.controls.label.setValue(this.component.templateField.label);
    this.imageFieldForm.controls.placeHolderText.setValue(this.component.templateField.placeHolderText);
    this.imageFieldForm.controls.helpTitle.setValue(this.component.templateField.helpTitle);
    this.imageFieldForm.controls.helpDescription.setValue(this.component.templateField.helpDescription);
    this.imageFieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.imageFieldForm.controls.hoverSortOrder.setValue(this.component.hoverSortOrder);
    this.imageFieldForm.controls.instanceSortOrder.setValue(this.component.instanceSortOrder);
    this.imageFieldForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.imageFieldForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.imageFieldForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.imageFieldForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.imageFieldForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.imageFieldForm.controls.defaultImageUrl.setValue(this.component.templateField.defaultImageUrl);
    this.imageFieldForm.controls.fileTypeBw.setValue(this.component.templateField.fileTypeBw);
    this.imageFieldForm.controls.minFileSize.setValue(this.component.templateField.minFileSize);
    this.imageFieldForm.controls.maxFileSize.setValue(this.component.templateField.maxFileSize);
    this.imageFieldForm.controls.isInherit.setValue(this.component.templateField.isInherit);
    this.imageFieldForm.controls.systemProperty.setValue(this.component.templateField.systemProperty);
    this.imageFieldForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.imageFieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.imageFieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.imageFieldForm.controls.useMaxWidth.setValue(this.component?.templateField?.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.imageFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.imageFieldForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.imageFieldForm.valid) {
      this.component.templateField.label = this.imageFieldForm.controls.label.value;
      this.component.templateField.placeHolderText = this.imageFieldForm.controls.placeHolderText.value;
      this.component.templateField.helpTitle = this.imageFieldForm.controls.helpTitle.value;
      this.component.templateField.helpDescription = this.imageFieldForm.controls.helpDescription.value;
      this.component.builderRowNumber = this.imageFieldForm.controls.rowNumber.value;
      this.component.hoverSortOrder = this.imageFieldForm.controls.hoverSortOrder.value;
      this.component.instanceSortOrder = this.imageFieldForm.controls.instanceSortOrder.value;
      this.component.templateField.isRequiredField = this.imageFieldForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.imageFieldForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.imageFieldForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.imageFieldForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.imageFieldForm.controls.isViewField.value;
      this.component.templateField.defaultImageUrl = this.imageFieldForm.controls.defaultImageUrl.value;
      this.component.templateField.fileTypeBw = this.imageFieldForm.controls.fileTypeBw.value.reduce((a: number, b: number) => a + b, 0);
      this.component.templateField.minFileSize = this.imageFieldForm.controls.minFileSize.value;
      this.component.templateField.maxFileSize = this.imageFieldForm.controls.maxFileSize.value;
      this.component.templateField.isInherit = this.imageFieldForm.controls.isInherit.value;
      this.component.templateField.isVisibleRepository = this.imageFieldForm.controls.isVisibleRepository.value;
      this.component.templateField.colspan = this.imageFieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.imageFieldForm.controls.colNumber.value;
      this.component.templateField.useMaxWidth = this.imageFieldForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
