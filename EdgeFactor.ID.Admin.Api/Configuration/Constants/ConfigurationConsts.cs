﻿namespace EdgeFactor.ID.Admin.Api.Configuration.Constants
{
    public class ConfigurationConsts
    {
        public const string ConfigurationDbConnectionStringKey = "ConfigurationDbConnection";

        public const string PersistedGrantDbConnectionStringKey = "PersistedGrantDbConnection";

        public const string IdentityDbConnectionStringKey = "IdentityDbConnection";

        public const string AdminLogDbConnectionStringKey = "AdminLogDbConnection";

        public const string AdminAuditLogDbConnectionStringKey = "AdminAuditLogDbConnection";

        public const string DataProtectionDbConnectionStringKey = "DataProtectionDbConnection";

        public const string ResourcesPath = "Resources";
    }
}







