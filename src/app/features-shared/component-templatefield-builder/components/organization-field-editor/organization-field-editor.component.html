<form [formGroup]="organizationFieldForm">
  <ion-grid>
    <ion-row>
      <ion-col>
        <app-text-input-control [backgroundColor]="'#292929'" [label]="'Row Number'" [placeHolder]="'Add field row number...'" formControlName="rowNumber" [type]="'number'"></app-text-input-control>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <app-text-input-control
          [backgroundColor]="'#292929'"
          [label]="'Column Number (Out of 12)'"
          [limit]="12"
          [placeHolder]="'Add column number (Out of 12)...'"
          formControlName="colNumber"
          [type]="'number'"></app-text-input-control>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <app-text-input-control
          [backgroundColor]="'#292929'"
          [label]="'Colspan (Out of 12)'"
          [limit]="12"
          [placeHolder]="'Add field colspan (Out of 12)...'"
          formControlName="colspan"
          [type]="'number'"></app-text-input-control>
      </ion-col>
    </ion-row>
  </ion-grid>
</form>
