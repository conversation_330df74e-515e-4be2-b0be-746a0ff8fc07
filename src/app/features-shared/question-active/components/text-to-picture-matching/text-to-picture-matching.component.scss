.parent-container {
  margin: 8px;

  .card-container {
    padding: 10px;
    background-color: #2d2e32;

    .top-description-row {
      margin-bottom: 10px;

      .description-col {
        .header {
          color: white;
          font-size: 14px;
          margin-bottom: 5px;
        }

        .input-container {
          border-radius: 5px;
          background-color: rgb(30, 30, 30);
        }
      }
    }

    .prompt-item-row {
      .inner-content {
        display: flex;

        .text-input-prompt-container {
          width: 100%;

          .prompt-header {
            margin-left: 5px;
            display: flex;
            justify-content: flex-start;
            color: white;
            font-size: 14px;
          }

          ion-input {
            border: 1px solid #4e4e4e;
            border-radius: 5px;
            margin-top: 0.2em;
            background: rgb(30, 30, 30);
            color: white;
            font-size: 16px;
            --padding-start: 8px !important;
          }
        }

        .middle-arrow-row {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 5px;
          margin-left: 5px;

          ion-icon {
            font-size: 20px;
            color: white;
          }
        }

        .text-input-answer-container {
          width: 100%;

          .answer-header {
            margin-left: 5px;
            display: flex;
            justify-content: flex-start;
            color: white;
            font-size: 14px;
          }

          ion-input {
            border: 1px solid #4e4e4e;
            border-radius: 5px;
            margin-top: 0.2em;
            background: rgb(30, 30, 30);
            color: white;
            font-size: 16px;
            --padding-start: 8px !important;
          }
        }

        .remove-container-right {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: 5px;

          ion-icon {
            font-size: 20px;
            color: white;
            cursor: pointer;
          }
        }

        .top-margin-down {
          margin-top: 15px;
        }
      }

      .div-line {
        hr {
          margin-bottom: 5px;
          border-top: 3px solid rgba(0, 0, 0, 0.5);
          border-style: dashed;
          width: 100%;
        }
      }
    }

    .add-item-col {
      padding: 0px;
      display: flex;

      align-items: center;

      ion-icon {
        color: rgb(249, 158, 0) !important;
        font-size: 30px;
        cursor: pointer;
      }

      .add-button-text {
        margin-left: 10px;
        font-size: 16px;
        color: white;
      }
    }
  }
}

.side-panel-input-padding {
  margin: 0px !important;

  ion-card {
    margin: 0px !important;
  }
}
