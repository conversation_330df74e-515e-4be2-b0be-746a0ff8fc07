import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-organization-field-editor',
    templateUrl: './organization-field-editor.component.html',
    styleUrls: ['./organization-field-editor.component.scss'],
    standalone: false
})
export class OrganizationFieldEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  organizationFieldForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(private formBuilder: UntypedFormBuilder) {}

  get isInheritControl(): AbstractControl {
    return this.organizationFieldForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.organizationFieldForm = this.formBuilder.group({
      rowNumber: [this.component?.builderRowNumber, Validators.required],
      colspan: [this.component?.templateField?.colspan, Validators.max(12)],
      colNumber: [this.component?.templateField?.colNumber, Validators.max(12)],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.organizationFieldForm) {
      return;
    }
    this.organizationFieldForm.controls.rowNumber.setValue(this.component.builderRowNumber);
    this.organizationFieldForm.controls.colspan.setValue(this.component?.templateField?.colspan);
    this.organizationFieldForm.controls.colNumber.setValue(this.component?.templateField?.colNumber);
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.organizationFieldForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.organizationFieldForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.organizationFieldForm.valid) {
      this.component.builderRowNumber = this.organizationFieldForm.controls.rowNumber.value;
      this.component.templateField.colspan = this.organizationFieldForm.controls.colspan.value;
      this.component.templateField.colNumber = this.organizationFieldForm.controls.colNumber.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
