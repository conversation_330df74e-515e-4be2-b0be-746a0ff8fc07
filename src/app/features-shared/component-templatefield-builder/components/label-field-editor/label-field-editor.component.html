@if (labelFieldForm) {
  <form [formGroup]="labelFieldForm">
    <ion-grid>
      <app-field-editor-base [fieldForm]="labelFieldForm"></app-field-editor-base>
      @if (component.componentType.name !== 'WYSIWYG') {
        <ion-row>
          <ion-col>
            <app-text-input-control [backgroundColor]="'#292929'" [label]="'Default Value'" [placeHolder]="'Add Default Value'" formControlName="defaultText"></app-text-input-control>
          </ion-col>
        </ion-row>
      }
      <ion-row>
        <ion-col>
          <ion-card>
            <ion-card-content>
              <app-field-checkboxes-base [baseForm]="labelFieldForm"></app-field-checkboxes-base>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVisibleRepository">Is Visible in Repository</mat-slide-toggle>
            </ion-card-content>
            <ion-card-content class="default-quill-container">
              @if (component.componentType.name === 'WYSIWYG') {
                <div class="heading">Default Value</div>
                <div class="quill-container">
                  <app-content-quill-editor [hideQuillPersonalize]="true" (dataChanged)="setDefaultValue($event)" [value]="labelFieldForm.controls['defaultText'].value"> </app-content-quill-editor>
                </div>
              }
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
