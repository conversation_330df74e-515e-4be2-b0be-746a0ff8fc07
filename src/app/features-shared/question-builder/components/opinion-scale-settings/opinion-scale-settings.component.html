<div class="container">
  <ion-label class="top-label" position="stacked">
    Ranking
    <span title="text" class="reqAsterisk">
      <ion-icon name="information-circle-outline"></ion-icon>
    </span>
  </ion-label>
  <ng-container [formGroup]="formGroup">
    <div class="scale-row">
      <ion-row class="full-width">
        <ion-col size="2"><app-text-input-control formControlName="scaleStart" [type]="'number'"></app-text-input-control></ion-col>
        <ion-col size="0.5" class="center-col"><ion-label class="label">to</ion-label></ion-col>
        <ion-col size="2"><app-text-input-control formControlName="scaleEnd" [type]="'number'"></app-text-input-control></ion-col>
      </ion-row>
    </div>
    <ion-card style="--background-color:{{ backgroundColor }};">
      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col class="center-content" size="2">
              <div class="center-content">
                <div class="alphabet">
                  <label>1</label>
                </div>
              </div>
            </ion-col>
            <ion-col style="align-items: flex-start" size="9" class="center-content col-padding">
              <div class="center-content">
                <ion-textarea placeholder="Type here" [maxlength]="maxlength" formControlName="scaleLabelOne"> </ion-textarea>
              </div>
            </ion-col>
            <ion-col size="1" maxlength="maxlength" class="center-content col-padding">
              <div class="center-content">{{ question.scaleLabelOne?.length }}/{{ maxlength }}</div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-card-content>
    </ion-card>
    <ion-card style="--background-color:{{ backgroundColor }};">
      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col class="center-content" size="2">
              <div class="center-content">
                <div class="alphabet">
                  <label>2</label>
                </div>
              </div>
            </ion-col>
            <ion-col style="align-items: flex-start" size="9" class="center-content col-padding">
              <div class="center-content">
                <ion-textarea placeholder="Type here" [maxlength]="maxlength" formControlName="scaleLabelTwo"> </ion-textarea>
              </div>
            </ion-col>
            <ion-col size="1" maxlength="maxlength" class="center-content col-padding">
              <div class="center-content">{{ question.scaleLabelTwo?.length }}/{{ maxlength }}</div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-card-content>
    </ion-card>
    <ion-card style="--background-color:{{ backgroundColor }};">
      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col class="center-content" size="2">
              <div class="center-content">
                <div class="alphabet">
                  <label>3</label>
                </div>
              </div>
            </ion-col>
            <ion-col style="align-items: flex-start" size="9" class="center-content col-padding">
              <div class="center-content">
                <ion-textarea placeholder="Type here" [maxlength]="maxlength" formControlName="scaleLabelThree"> </ion-textarea>
              </div>
            </ion-col>
            <ion-col size="1" maxlength="maxlength" class="center-content col-padding">
              <div class="center-content">{{ question.scaleLabelThree?.length }}/{{ maxlength }}</div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-card-content>
    </ion-card>
    @if (question.questionType.name === 'Interest Profiler') {
      <ion-label class="top-label" position="stacked"> Purpose </ion-label>
      <ion-row>
        <ion-col size="3" class="center-col"><ion-label class="top-label">The question: </ion-label></ion-col>
        <ion-col size="9" class="center-row">
          @if (purposes) {
            <app-select-option-control [options]="purposes" [placeHolder]="'--select--'" [backgroundColor]="'#181818'" formControlName="purpose"></app-select-option-control>
          }
        </ion-col>
      </ion-row>
      <ion-label class="top-label" position="stacked">Now select the occupation tag that this question is measuring interest in.</ion-label>
      @if (options$ | async; as occupations) {
        <app-tag-select-option-control
          [options]="occupations"
          [label]="'Occupation'"
          [placeHolder]="'--select--'"
          formControlName="occupation"
          [textValue]="question.measuredTagId"
          [backgroundColor]="'#181818'"></app-tag-select-option-control>
      }
    }
  </ng-container>
</div>
