import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IComponent } from '@app/core/contracts/contract';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Subject, Subscription, takeUntil } from 'rxjs';

@Component({
    selector: 'app-organization-network-editor',
    templateUrl: './organization-network-editor.component.html',
    styleUrls: ['./organization-network-editor.component.scss'],
    standalone: false
})
export class OrganizationNetworkEditorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() component: IComponent;
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  orgNetworkForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();
  formValueChanges$: Subscription;

  constructor(
    private formBuilder: UntypedFormBuilder,
    public builderService: BuilderService
  ) {}

  get isInheritControl(): AbstractControl {
    return this.orgNetworkForm.controls.isInherit;
  }

  ngOnInit() {
    this.createForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  createForm() {
    this.orgNetworkForm = this.formBuilder.group({
      label: [this.component?.templateField?.label, Validators.required],
      placeHolderText: [this.component?.templateField?.placeHolderText, Validators.required],
      isRequiredField: [this.component?.templateField?.isRequiredField ?? false],
      isPreviewField: [this.component?.templateField?.isPreviewField ?? true],
      isBuilderEnabled: [this.component?.templateField?.isBuilderEnabled ?? true],
      isHoverField: [this.component?.templateField?.isHoverField ?? false],
      isViewField: [this.component?.templateField?.isViewField ?? true],
      isVisibleRepository: [this.component?.templateField?.isVisibleRepository ?? false],
      useMaxWidth: [this.component?.templateField?.useMaxWidth ?? false],
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.orgNetworkForm) {
      return;
    }

    this.orgNetworkForm.controls.label.setValue(this.component.templateField.label);
    this.orgNetworkForm.controls.placeHolderText.setValue(this.component.templateField.placeHolderText);
    this.orgNetworkForm.controls.isRequiredField.setValue(this.component.templateField.isRequiredField);
    this.orgNetworkForm.controls.isBuilderEnabled.setValue(this.component.templateField.isBuilderEnabled);
    this.orgNetworkForm.controls.isPreviewField.setValue(this.component.templateField.isPreviewField);
    this.orgNetworkForm.controls.isHoverField.setValue(this.component.templateField.isHoverField);
    this.orgNetworkForm.controls.isViewField.setValue(this.component.templateField.isViewField);
    this.orgNetworkForm.controls.isVisibleRepository.setValue(this.component.templateField.isVisibleRepository);
    this.orgNetworkForm.controls.useMaxWidth.setValue(this.component.templateField.useMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.orgNetworkForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.orgNetworkForm.valid);
      this.setValues();
    });
  }

  setValues() {
    if (this.orgNetworkForm.valid) {
      this.component.templateField.label = this.orgNetworkForm.controls.label.value;
      this.component.templateField.placeHolderText = this.orgNetworkForm.controls.placeHolderText.value;
      this.component.templateField.isRequiredField = this.orgNetworkForm.controls.isRequiredField.value;
      this.component.templateField.isBuilderEnabled = this.orgNetworkForm.controls.isBuilderEnabled.value;
      this.component.templateField.isPreviewField = this.orgNetworkForm.controls.isPreviewField.value;
      this.component.templateField.isHoverField = this.orgNetworkForm.controls.isHoverField.value;
      this.component.templateField.isViewField = this.orgNetworkForm.controls.isViewField.value;
      this.component.templateField.isVisibleRepository = this.orgNetworkForm.controls.isVisibleRepository.value;
      this.component.templateField.useMaxWidth = this.orgNetworkForm.controls.useMaxWidth.value;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
