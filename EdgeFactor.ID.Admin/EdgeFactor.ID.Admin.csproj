﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <Version>2.0.1</Version>
        <Authors><PERSON></Authors>
        <LangVersion>latest</LangVersion>
        <UserSecretsId>8fe260ca-ef4c-4fa3-9364-029146f8d339</UserSecretsId>
        <DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <DockerfileContext>..\..</DockerfileContext>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AspNetCore.HealthChecks.MySql" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.OpenIdConnectServer" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="6.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.UI" Version="6.0.4" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="6.0.5" />
        <PackageReference Include="IdentityServer4.EntityFramework" Version="4.1.2" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="6.0.11" />
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="6.0.11" />
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="6.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="6.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="6.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.11">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="AutoMapper" Version="10.1.1" />        
        <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
        <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.17.0" />
        <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.10" />
        <PackageReference Include="NWebsec.AspNetCore.Middleware" Version="3.0.0" />
        <PackageReference Include="Serilog" Version="2.12.0" />
        <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
        <PackageReference Include="Serilog.Extensions.Hosting" Version="5.0.1" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="2.2.0" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="3.4.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
        <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.Seq" Version="5.2.2" />
        <PackageReference Include="Skoruba.IdentityServer4.Admin.BusinessLogic" Version="2.1.0" />
        <PackageReference Include="Skoruba.IdentityServer4.Admin.BusinessLogic.Identity" Version="2.1.0" />
        <PackageReference Include="Skoruba.IdentityServer4.Admin.UI" Version="2.1.0" />
    </ItemGroup>

    <ItemGroup>
        <DotNetCliToolReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Tools" Version="2.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\EdgeFactor.ID.Admin.EntityFramework.MySql\EdgeFactor.ID.Admin.EntityFramework.MySql.csproj" />
        <ProjectReference Include="..\EdgeFactor.ID.Admin.EntityFramework.PostgreSQL\EdgeFactor.ID.Admin.EntityFramework.PostgreSQL.csproj" />
        <ProjectReference Include="..\EdgeFactor.ID.Admin.EntityFramework.Shared\EdgeFactor.ID.Admin.EntityFramework.Shared.csproj" />
        <ProjectReference Include="..\EdgeFactor.ID.Admin.EntityFramework.SqlServer\EdgeFactor.ID.Admin.EntityFramework.SqlServer.csproj" />
        <ProjectReference Include="..\EdgeFactor.ID.Shared\EdgeFactor.ID.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="log\" />
    </ItemGroup>

</Project>



















